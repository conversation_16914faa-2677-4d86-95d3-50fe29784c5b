<?php

namespace Simbi\Tls\Services;

use Simbi\Tls\Repositories\UserRepository;
use Simbi\Tls\Repositories\WalletRepository;
use Exception;

class AuthService
{
    private UserRepository $userRepository;
    private WalletRepository $walletRepository;
    private TronService $tronService;
    private string $jwtSecret;

    public function __construct(UserRepository $userRepository, WalletRepository $walletRepository, TronService $tronService)
    {
        $this->userRepository = $userRepository;
        $this->walletRepository = $walletRepository;
        $this->tronService = $tronService;
        $this->jwtSecret = \Simbi\Tls\Config\Config::get('JWT_SECRET') ?: 'default-secret-key-change-this';
    }    public function register(string $email, string $password): array
    {
        // Validate email
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return ['error' => 'Invalid email format', 'code' => 400];
        }

        // Validate password
        if (strlen($password) < 6) {
            return ['error' => 'Password must be at least 6 characters long', 'code' => 400];
        }

        // Check if email already exists
        if ($this->userRepository->emailExists($email)) {
            return ['error' => 'Email already registered', 'code' => 409];
        }

        try {
            // Hash password
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);

            // Create user
            $userId = $this->userRepository->create($email, $passwordHash);

            if (!$userId) {
                return ['error' => 'Failed to create user', 'code' => 500];
            }

            // Automatically create wallet for the new user
            $walletResult = $this->createWalletForUser($userId);
            if (!$walletResult['success']) {
                // If wallet creation fails, we should consider rolling back user creation
                // For now, we'll log the error but continue with registration
                error_log("Failed to create wallet for user $userId: " . $walletResult['error']);
            }

            // Generate JWT token
            $token = $this->generateJWT($userId, $email);

            $response = [
                'success' => true,
                'message' => 'User registered successfully',
                'user' => [
                    'id' => $userId,
                    'email' => $email
                ],
                'token' => $token
            ];

            // Include wallet address in response if wallet was created successfully
            if ($walletResult['success']) {
                $response['wallet'] = [
                    'address' => $walletResult['address'],
                    'message' => 'Wallet created automatically'
                ];
            }

            return $response;
        } catch (Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            return ['error' => 'Registration failed', 'code' => 500];        }
    }

    private function createWalletForUser(int $userId): array
    {
        try {
            // Check if wallet already exists for this user
            $existingWallet = $this->walletRepository->findByUserId($userId);
            if ($existingWallet) {
                return [
                    'success' => true,
                    'address' => $existingWallet['address'],
                    'message' => 'Wallet already exists'
                ];
            }

            // Generate new wallet
            $account = $this->tronService->generateAddress();

            // Save to database
            $success = $this->walletRepository->create(
                $userId,
                $account['address'],
                $account['privateKey']
            );

            if (!$success) {
                return ['success' => false, 'error' => 'Failed to save wallet to database'];
            }

            return [
                'success' => true,
                'address' => $account['address'],
                'message' => 'Wallet created successfully'
            ];
        } catch (Exception $e) {
            error_log("Create wallet error for user $userId: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    public function login(string $email, string $password): array
    {
        try {
            // Find user by email
            $user = $this->userRepository->findByEmail($email);

            if (!$user) {
                return ['error' => 'Invalid credentials', 'code' => 401];
            }

            // Verify password
            if (!password_verify($password, $user['password_hash'])) {
                return ['error' => 'Invalid credentials', 'code' => 401];
            }

            // Generate JWT token
            $token = $this->generateJWT($user['id'], $user['email']);

            return [
                'success' => true,
                'message' => 'Login successful',
                'user' => [
                    'id' => $user['id'],
                    'email' => $user['email']
                ],
                'token' => $token
            ];
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return ['error' => 'Login failed', 'code' => 500];
        }
    }

    public function validateToken(string $token): ?array
    {
        try {
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return null;
            }

            $header = json_decode(base64_decode($parts[0]), true);
            $payload = json_decode(base64_decode($parts[1]), true);
            $signature = $parts[2];

            // Verify signature
            $expectedSignature = base64_encode(hash_hmac('sha256', $parts[0] . '.' . $parts[1], $this->jwtSecret, true));
            
            if (!hash_equals($expectedSignature, $signature)) {
                return null;
            }

            // Check expiration
            if (isset($payload['exp']) && $payload['exp'] < time()) {
                return null;
            }

            return $payload;
        } catch (Exception $e) {
            error_log("Token validation error: " . $e->getMessage());
            return null;
        }
    }

    private function generateJWT(int $userId, string $email): string
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode([
            'user_id' => $userId,
            'email' => $email,
            'iat' => time(),
            'exp' => time() + (24 * 60 * 60) // 24 hours
        ]);

        $headerEncoded = base64_encode($header);
        $payloadEncoded = base64_encode($payload);
        $signature = base64_encode(hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $this->jwtSecret, true));

        return $headerEncoded . '.' . $payloadEncoded . '.' . $signature;
    }

    public function getUserFromToken(string $token): ?array
    {
        $payload = $this->validateToken($token);
        
        if (!$payload) {
            return null;
        }

        return $this->userRepository->findById($payload['user_id']);
    }

    public function requestPasswordReset(string $email): array
    {
        try {
            // Check if user exists
            $user = $this->userRepository->findByEmail($email);
            if (!$user) {
                // Don't reveal if email exists for security
                return [
                    'success' => true,
                    'message' => 'If the email exists, a password reset link has been sent'
                ];
            }

            // Generate reset token
            $token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour expiry

            // Save reset token
            $success = $this->userRepository->createPasswordResetToken($user['id'], $token, $expires);
            
            if (!$success) {
                return ['error' => 'Failed to create reset token', 'code' => 500];
            }

            // In a real implementation, you would send an email here
            // For now, we'll return the token for testing
            return [
                'success' => true,
                'message' => 'Password reset token created',
                'token' => $token, // Remove this in production
                'expires_at' => $expires
            ];
        } catch (Exception $e) {
            error_log("Password reset request error: " . $e->getMessage());
            return ['error' => 'Internal server error', 'code' => 500];
        }
    }

    public function resetPassword(string $token, string $newPassword): array
    {
        try {
            // Validate password
            if (strlen($newPassword) < 6) {
                return ['error' => 'Password must be at least 6 characters long', 'code' => 400];
            }

            // Find user by reset token
            $user = $this->userRepository->findByPasswordResetToken($token);
            if (!$user) {
                return ['error' => 'Invalid or expired reset token', 'code' => 400];
            }

            // Check if token is expired
            if (strtotime($user['password_reset_expires']) < time()) {
                return ['error' => 'Reset token has expired', 'code' => 400];
            }

            // Hash new password
            $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);

            // Update password and clear reset token
            $success = $this->userRepository->updatePassword($user['id'], $passwordHash);
            
            if (!$success) {
                return ['error' => 'Failed to update password', 'code' => 500];
            }

            // Clear reset token
            $this->userRepository->clearPasswordResetToken($user['id']);

            return [
                'success' => true,
                'message' => 'Password reset successfully'
            ];
        } catch (Exception $e) {
            error_log("Password reset error: " . $e->getMessage());
            return ['error' => 'Internal server error', 'code' => 500];
        }
    }

    public function changePassword(int $userId, string $currentPassword, string $newPassword): array
    {
        try {
            // Get user
            $user = $this->userRepository->findById($userId);
            if (!$user) {
                return ['error' => 'User not found', 'code' => 404];
            }

            // Verify current password
            if (!password_verify($currentPassword, $user['password_hash'])) {
                return ['error' => 'Current password is incorrect', 'code' => 400];
            }

            // Validate new password
            if (strlen($newPassword) < 6) {
                return ['error' => 'New password must be at least 6 characters long', 'code' => 400];
            }

            // Hash new password
            $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);

            // Update password
            $success = $this->userRepository->updatePassword($userId, $passwordHash);
            
            if (!$success) {
                return ['error' => 'Failed to update password', 'code' => 500];
            }

            return [
                'success' => true,
                'message' => 'Password changed successfully'
            ];
        } catch (Exception $e) {
            error_log("Password change error: " . $e->getMessage());
            return ['error' => 'Internal server error', 'code' => 500];
        }
    }
}
