/* Dashboard page styles */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.app-header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.header-actions .btn {
    padding: 8px 16px;
    font-size: 14px;
    min-height: 36px;
}

/* Navigation */
.app-nav {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 0;
    position: sticky;
    top: 64px;
    z-index: 99;
}

.app-nav {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.app-nav::-webkit-scrollbar {
    display: none;
}

.nav-btn {
    background: none;
    border: none;
    padding: 16px 20px;
    cursor: pointer;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-bottom: 2px solid transparent;
    font-size: 14px;
}

.nav-btn:hover {
    color: #495057;
    background-color: #f8f9fa;
}

.nav-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

/* Main content */
.app-main {
    flex: 1;
    padding: 24px 0;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding-left: 20px;
    padding-right: 20px;
}

.tab-content {
    display: none;
}

.tab-content {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
    opacity: 1;
    transform: translateY(0);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Dashboard grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

/* Balance card */
.balance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    pointer-events: none;
}

.balance-card h3 {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 16px;
    font-size: 1.1rem;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.balance-currency {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Stats card */
.stats-card {
    background: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #6c757d;
    font-size: 14px;
}

.stat-value {
    font-weight: 600;
    color: #495057;
    font-size: 16px;
}

/* Recent transactions */
.recent-transactions {
    background: white;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f1f3f4;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-info {
    flex: 1;
}

.transaction-type {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.transaction-date {
    color: #6c757d;
    font-size: 13px;
    margin-top: 4px;
}

.transaction-amount {
    font-weight: 600;
    font-size: 16px;
}

.transaction-amount.positive {
    color: #28a745;
}

.transaction-amount.negative {
    color: #dc3545;
}

/* Wallet section */
.wallet-section {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

.wallet-address,
.wallet-balance {
    margin-bottom: 16px;
}

.wallet-address label,
.wallet-balance label {
    display: block;
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 8px;
    font-weight: 500;
}

.address-display,
.balance-display {
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    word-break: break-all;
    border: 1px solid #e9ecef;
}

.wallet-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Transactions section */
.transactions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
}

.transaction-filters {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.transaction-filters select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.transaction-list {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.transaction-row {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 16px;
    padding: 16px;
    border-bottom: 1px solid #f1f3f4;
    align-items: center;
}

.transaction-row:last-child {
    border-bottom: none;
}

.transaction-details {
    min-width: 0;
}

.transaction-hash {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #6c757d;
    word-break: break-all;
}

.transaction-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.transaction-status.confirmed {
    background-color: #d4edda;
    color: #155724;
}

.transaction-status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.transaction-status.failed {
    background-color: #f8d7da;
    color: #721c24;
}

/* Deposit section */
.deposit-info {
    text-align: center;
    margin-bottom: 32px;
}

.deposit-address {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    border: 2px dashed #dee2e6;
}

.deposit-address div {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    word-break: break-all;
    margin-bottom: 12px;
}

.deposit-qr {
    margin-top: 20px;
    text-align: center;
}

.manual-deposit {
    border-top: 1px solid #e9ecef;
    padding-top: 24px;
    margin-top: 24px;
}

.manual-deposit h4 {
    margin-bottom: 16px;
    color: #495057;
}

/* Profile modal */
.profile-info {
    margin-bottom: 24px;
}

.profile-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.profile-field:last-child {
    border-bottom: none;
}

.profile-field label {
    font-weight: 500;
    color: #6c757d;
}

.profile-actions {
    text-align: center;
}

/* QR Code styles */
.qr-code-container {
    text-align: center;
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin: 1rem 0;
}

.qr-code-image {
    max-width: 200px;
    height: auto;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qr-code-address {
    font-family: monospace;
    font-size: 0.875rem;
    color: var(--text-muted);
    word-break: break-all;
    margin: 0.5rem 0;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.copy-success-message {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

#depositQR {
    margin-top: 1rem;
}

/* Sticky Footer Menu */
.footer-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e9ecef;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: block !important; /* Always visible on all screen sizes */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transform: translateY(0) !important; /* Always shown */
    transition: transform 0.3s ease;
}

.footer-menu.show {
    transform: translateY(0);
}

.footer-nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 12px 8px 8px 8px;
    max-width: 100%;
    background: rgba(255, 255, 255, 0.95);
}

.footer-btn {
    background: none;
    border: none;
    padding: 8px 4px;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
    border-radius: 8px;
    position: relative;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.footer-btn:hover,
.footer-btn:focus {
    color: #495057;
    background-color: rgba(102, 126, 234, 0.1);
    outline: none;
}

.footer-btn:active {
    transform: scale(0.95);
}

.footer-btn.active {
    color: #667eea;
}

.footer-btn.active::before {
    content: '';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 3px;
    background: #667eea;
    border-radius: 2px;
}

.footer-icon {
    width: 22px;
    height: 22px;
    transition: all 0.3s ease;
}

.footer-btn.active .footer-icon {
    transform: scale(1.1);
}

.footer-btn span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    margin-top: 2px;
}

/* Add bottom padding to main content when footer is visible */
.app-container.with-footer {
    padding-bottom: 70px;
}

/* Safe area insets for modern devices (iPhone X, etc.) */
@supports (padding: max(0px)) {
    .footer-menu {
        padding-bottom: max(8px, env(safe-area-inset-bottom));
    }
    
    .app-container {
        padding-bottom: calc(70px + max(0px, env(safe-area-inset-bottom)));
    }
}

/* Dark mode support for footer */
@media (prefers-color-scheme: dark) {
    .footer-menu {
        background: #1a1a1a;
        border-top-color: #333;
    }
    
    .footer-nav {
        background: rgba(26, 26, 26, 0.95);
    }
    
    .footer-btn {
        color: #a0a0a0;
    }
    
    .footer-btn:hover {
        color: #ffffff;
        background-color: rgba(102, 126, 234, 0.2);
    }
    
    .footer-btn.active {
        color: #667eea;
    }
}

/* Desktop and all screen sizes - always show footer menu */
@media (min-width: 769px) {
    .footer-menu {
        display: block !important;
        transform: translateY(0) !important;
    }
    
    /* Ensure navigation is visible on desktop */
    .app-nav {
        display: flex;
    }
    
    /* Maintain bottom padding for footer on desktop */
    .app-container {
        padding-bottom: 80px;
    }
    
    .app-main {
        padding-bottom: 80px;
    }
}

/* Desktop and all screen sizes - always show footer menu */
@media (min-width: 769px) {
    .footer-menu {
        display: block !important;
        transform: translateY(0) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    /* Ensure navigation is visible on desktop */
    .app-nav {
        display: flex;
    }
    
    /* Maintain bottom padding for footer on desktop */
    .app-container {
        padding-bottom: 80px;
    }
    
    .app-main {
        padding-bottom: 80px;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    /* Hide regular navigation on mobile */
    .app-nav {
        display: none;
    }
    
    /* Show footer menu on mobile */
    .footer-menu {
        display: block !important;
        transform: translateY(0);
    }
    
    /* Add padding to prevent content from being hidden behind footer */
    .app-container {
        padding-bottom: 70px;
    }
    
    .header-content {
        padding: 0 16px;
    }
    
    .app-main {
        padding: 16px;
        padding-bottom: 80px; /* Extra padding for footer */
    }
    
    .balance-amount {
        font-size: 2rem;
    }
    
    .wallet-actions {
        flex-direction: column;
    }
    
    .wallet-actions .btn {
        width: 100%;
    }
    
    .transactions-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .transaction-filters {
        width: 100%;
    }
    
    .transaction-filters select {
        flex: 1;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 12px;
    }
    
    .app-main {
        padding: 12px;
    }
    
    .header-actions {
        gap: 6px;
    }
    
    .header-actions .btn {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .footer-btn {
        padding: 6px 2px;
        font-size: 9px;
        min-width: 50px;
    }
    
    .footer-icon {
        width: 18px;
        height: 18px;
    }
}

@media (min-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .balance-card {
        grid-column: 1 / -1;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .wallet-section {
        grid-template-columns: 1fr 1fr;
    }
    
    .manual-deposit {
        grid-column: 1 / -1;
    }
    
    .transaction-row {
        grid-template-columns: auto 1fr auto auto;
    }
}

@media (min-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 2fr 1fr;
    }
    
    .balance-card {
        grid-column: 1 / -1;
    }
    
    .recent-transactions {
        grid-column: 1;
        grid-row: 2;
    }
    
    .stats-card {
        grid-column: 2;
        grid-row: 2;
    }
}

/* Profile Page Styles */
.profile-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.profile-card {
    margin-bottom: 24px;
}

.profile-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.profile-header h2 {
    margin: 0 0 4px 0;
    color: #212529;
    font-size: 1.75rem;
    font-weight: 600;
}

.profile-header p {
    margin: 0;
    color: #6c757d;
    font-size: 0.95rem;
}

.profile-section {
    margin-bottom: 32px;
}

.profile-section h3 {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.profile-info {
    display: grid;
    gap: 16px;
}

.profile-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.profile-field:last-child {
    border-bottom: none;
}

.profile-field label {
    font-weight: 500;
    color: #495057;
    margin: 0;
}

.profile-field span {
    color: #212529;
    font-weight: 400;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.security-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.security-actions .btn {
    display: flex;
    align-items: center;
    gap: 8px;
}

.wallet-summary {
    display: grid;
    gap: 16px;
    margin-bottom: 20px;
}

.wallet-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.wallet-stat:last-child {
    border-bottom: none;
}

.wallet-stat label {
    font-weight: 500;
    color: #495057;
    margin: 0;
}

.wallet-stat span {
    color: #212529;
    font-weight: 400;
}

.wallet-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.change-password-card {
    border-left: 4px solid #007bff;
}

.change-password-card .profile-header {
    margin-bottom: 20px;
}

.change-password-card .profile-header h3 {
    margin: 0;
    color: #007bff;
}

.password-requirements {
    margin-top: 4px;
}

.password-requirements small {
    color: #6c757d;
    font-size: 0.8rem;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

/* Mobile responsive styles for profile page */
@media (max-width: 768px) {
    .profile-page {
        padding: 12px;
    }
    
    .profile-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .profile-field {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .wallet-stat {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .security-actions,
    .wallet-actions {
        flex-direction: column;
    }
    
    .security-actions .btn,
    .wallet-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions .btn {
        width: 100%;
    }
}

/* FINAL OVERRIDE - Ensure footer is always visible on all screen sizes */
.footer-menu {
    display: block !important;
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    transform: translateY(0) !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 1000 !important;
}
