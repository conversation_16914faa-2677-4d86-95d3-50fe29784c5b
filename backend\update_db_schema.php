<?php
/**
 * Database Schema Update Script
 * Production-ready formatting and structure.
 */

require 'vendor/autoload.php';

use Simbi\Tls\Config\Database;
use Simbi\Tls\Config\Config;

Config::load();

try {
    $pdo = Database::getConnection();
    if (!$pdo) {
        throw new Exception("Failed to connect to database");
    }

    // Disable foreign key checks for safe updates
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");

    // Add new columns to users table if they don't exist
    $userColumns = [
        'is_admin' => 'BOOLEAN DEFAULT FALSE',
        'is_active' => 'BOOLEAN DEFAULT TRUE',
        'password_reset_token' => 'VARCHAR(255) NULL',
        'password_reset_expires' => 'TIMESTAMP NULL',
        'email_verified' => 'BOOLEAN DEFAULT FALSE',
        'email_verification_token' => 'VARCHAR(255) NULL'
    ];

    foreach ($userColumns as $column => $definition) {
        try {
            $stmt = $pdo->prepare("SHOW COLUMNS FROM users LIKE ?");
            $stmt->execute([$column]);
            if ($stmt->rowCount() == 0) {
                $pdo->exec("ALTER TABLE users ADD COLUMN $column $definition");
            }
        } catch (Exception $e) {
            error_log("Error adding column '$column': " . $e->getMessage());
        }
    }

    // Add indexes to users table
    $userIndexes = [
        'idx_reset_token' => 'password_reset_token',
        'idx_verification_token' => 'email_verification_token'
    ];

    foreach ($userIndexes as $indexName => $column) {
        try {
            $pdo->exec("CREATE INDEX $indexName ON users ($column)");
        } catch (Exception $e) {
            // Index may already exist, ignore error
        }
    }

    // Add new columns to transactions table if they don't exist
    $transactionColumns = [
        'fee' => 'DECIMAL(20, 6) DEFAULT 0.000000',
        'from_address' => 'VARCHAR(255) NULL',
        'to_address' => 'VARCHAR(255) NULL',
        'block_number' => 'BIGINT NULL',
        'notes' => 'TEXT NULL',
        'confirmations' => 'INT DEFAULT 0'
    ];

    foreach ($transactionColumns as $column => $definition) {
        try {
            $stmt = $pdo->prepare("SHOW COLUMNS FROM transactions LIKE ?");
            $stmt->execute([$column]);
            if ($stmt->rowCount() == 0) {
                $pdo->exec("ALTER TABLE transactions ADD COLUMN $column $definition");
            }
        } catch (Exception $e) {
            error_log("Error adding column '$column': " . $e->getMessage());
        }
    }

    // Create admin logs table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL,
            action VARCHAR(255) NOT NULL,
            target_type VARCHAR(100) NULL,
            target_id INT NULL,
            details JSON NULL,
            ip_address VARCHAR(45) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_admin_id (admin_id),
            INDEX idx_created_at (created_at)
        )
    ");

    // Create password resets table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS password_resets (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL,
            token VARCHAR(255) NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            used BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_token (token),
            INDEX idx_expires (expires_at)
        )
    ");

    // Create sweeps table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS sweeps (
            id INT AUTO_INCREMENT PRIMARY KEY,
            wallet_id INT NOT NULL,
            amount DECIMAL(20, 6) NOT NULL,
            status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (wallet_id) REFERENCES wallets(id) ON DELETE CASCADE
        )
    ");

    // Re-enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");

    // Verify all expected tables exist
    $expectedTables = ['users', 'wallets', 'transactions', 'admin_logs', 'password_resets', 'sweeps'];
    foreach ($expectedTables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->rowCount() == 0) {
            error_log("Table '$table' missing after schema update");
        }
    }

} catch (Exception $e) {
    error_log("Database schema update error: " . $e->getMessage());
    exit(1);
}
