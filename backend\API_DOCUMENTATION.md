# TRON Wallet API Documentation

## Overview
A secure TRON wallet API with JWT-based authentication, built using PHP with a clean repository pattern architecture.

## Base URL
```
http://localhost:8000
```

## Authentication
The API uses JW<PERSON> (JSON Web Tokens) for authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Endpoints

### Public Endpoints (No Authentication Required)

#### 1. User Registration
```http
POST /api/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```
**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "user": {
    "id": 1,
    "email": "<EMAIL>"
  },
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

#### 2. Password Reset Request
```http
POST /api/password-reset-request
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```
**Response:**
```json
{
  "success": true,
  "message": "Password reset email sent"
}
```

#### 3. Password Reset
```http
POST /api/password-reset
Content-Type: application/json

{
  "token": "reset_token_here",
  "new_password": "newpassword123"
}
```
**Response:**
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

#### 4. User Login
```http
POST /api/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```
**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "user": {
    "id": 1,
    "email": "<EMAIL>"
  },
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### Protected Endpoints (Authentication Required)

#### 4. Get User Profile
```http
GET /api/me
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "created_at": "2025-06-04 20:41:31"
  }
}
```

#### 5. Create Wallet
```http
POST /api/create-wallet
Authorization: Bearer <token>
Content-Type: application/json
```
**Response:**
```json
{
  "success": true,
  "address": "41935c7638b684520d1caa5d22d5cd7492cab38df6",
  "message": "Wallet created successfully"
}
```

#### 6. Get Balance
```http
POST /api/balance
Authorization: Bearer <token>
Content-Type: application/json
```
**Response:**
```json
{
  "success": true,
  "address": "41935c7638b684520d1caa5d22d5cd7492cab38df6",
  "balance": "1000000",
  "balance_formatted": "1.000000"
}
```

#### 7. Withdraw Funds
```http
POST /api/withdraw
Authorization: Bearer <token>
Content-Type: application/json

{
  "to": "TRX9Pjwb5hTTLceFuzpyoG4SI4hcgkdMpsN",
  "amount": 1.5
}
```
**Response:**
```json
{
  "success": true,
  "transaction_hash": "0x...",
  "message": "Withdrawal successful"
}
```

#### 8. Sweep Funds
```http
POST /api/sweep-funds
Authorization: Bearer <token>
Content-Type: application/json
```
**Response:**
```json
{
  "success": true,
  "transaction_hash": "0x...",
  "amount_swept": "1.500000",
  "message": "Funds swept successfully"
}
```

#### 9. Change Password
```http
POST /api/change-password
Authorization: Bearer <token>
Content-Type: application/json

{
  "current_password": "oldpassword123",
  "new_password": "newpassword123"
}
```
**Response:**
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

#### 10. Get Transaction History
```http
GET /api/transactions?limit=10&offset=0&type=deposit&status=confirmed
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "transactions": [
    {
      "id": 1,
      "transaction_hash": "abc123...",
      "type": "deposit",
      "amount": "10.500000",
      "status": "confirmed",
      "created_at": "2025-06-04 20:41:31"
    }
  ],
  "pagination": {
    "limit": 10,
    "offset": 0,
    "has_more": false
  }
}
```

#### 11. Record Deposit Transaction
```http
POST /api/transactions/deposit
Authorization: Bearer <token>
Content-Type: application/json

{
  "wallet_address": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
  "transaction_hash": "abc123...",
  "amount": "10.500000",
  "from_address": "TFromAddress123"
}
```
**Response:**
```json
{
  "success": true,
  "transaction_id": 1,
  "message": "Deposit recorded successfully"
}
```

#### 12. Get Transaction Statistics
```http
GET /api/transactions/statistics
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "statistics": {
    "total_transactions": 25,
    "total_deposits": 15,
    "total_withdrawals": 10,
    "total_volume": "1250.500000"
  }
}
```

#### 13. Get Transaction Details
```http
GET /api/transactions/{id}
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "transaction": {
    "id": 1,
    "transaction_hash": "abc123...",
    "type": "deposit",
    "amount": "10.500000",
    "fee": "0.100000",
    "from_address": "TFromAddress123",
    "to_address": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
    "status": "confirmed",
    "confirmations": 19,
    "created_at": "2025-06-04 20:41:31"
  }
}
```

#### 14. Get Wallet Transactions
```http
GET /api/wallets/{id}/transactions?limit=10&offset=0
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "transactions": [
    {
      "id": 1,
      "transaction_hash": "abc123...",
      "type": "deposit",
      "amount": "10.500000",
      "status": "confirmed"
    }
  ],
  "wallet": {
    "id": 1,
    "address": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
    "balance": "1000.500000"
  },
  "pagination": {
    "limit": 10,
    "offset": 0,
    "has_more": false
  }
}
```

### Admin Endpoints (Admin Authentication Required)

#### 15. Get System Statistics
```http
GET /api/admin/statistics
Authorization: Bearer <admin_token>
```
**Response:**
```json
{
  "success": true,
  "statistics": {
    "total_users": 150,
    "active_users": 140,
    "total_wallets": 145,
    "total_transactions": 1250,
    "total_volume": "125000.500000",
    "recent_activity": {
      "new_users_24h": 5,
      "new_wallets_24h": 4,
      "transactions_24h": 25
    }
  }
}
```

#### 16. Get User List
```http
GET /api/admin/users?limit=20&offset=0&status=active
Authorization: Bearer <admin_token>
```
**Response:**
```json
{
  "success": true,
  "users": [
    {
      "id": 1,
      "email": "<EMAIL>",
      "is_admin": false,
      "is_active": true,
      "created_at": "2025-06-04 20:41:31",
      "wallet_count": 1,
      "transaction_count": 5
    }
  ],
  "pagination": {
    "limit": 20,
    "offset": 0,
    "total": 150
  }
}
```

#### 17. Update User Status
```http
PUT /api/admin/users/{id}/status
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "is_active": false
}
```
**Response:**
```json
{
  "success": true,
  "message": "User status updated successfully"
}
```

#### 18. Promote User to Admin
```http
POST /api/admin/users/{id}/promote
Authorization: Bearer <admin_token>
```
**Response:**
```json
{
  "success": true,
  "message": "User promoted to admin successfully"
}
```

#### 19. Get Activity Logs
```http
GET /api/admin/logs?limit=50&offset=0
Authorization: Bearer <admin_token>
```
**Response:**
```json
{
  "success": true,
  "logs": [
    {
      "id": 1,
      "admin_id": 1,
      "action": "user_status_update",
      "target_user_id": 5,
      "details": "User deactivated",
      "created_at": "2025-06-04 20:41:31"
    }
  ]
}
```

#### 20. Get Transaction Statistics (Admin)
```http
GET /api/admin/transactions/statistics
Authorization: Bearer <admin_token>
```
**Response:**
```json
{
  "success": true,
  "statistics": {
    "total_transactions": 1250,
    "successful_transactions": 1200,
    "failed_transactions": 50,
    "total_volume": "125000.500000",
    "average_transaction_size": "100.000400",
    "transactions_by_type": {
      "deposits": 800,
      "withdrawals": 400,
      "sweeps": 50
    }
  }
}
```

#### 21. System Health Check
```http
GET /api/admin/health
Authorization: Bearer <admin_token>
```
**Response:**
```json
{
  "success": true,
  "health": {
    "database": "healthy",
    "tron_network": "connected",
    "api_status": "operational",
    "uptime": "24 hours",
    "memory_usage": "45%",
    "response_time": "120ms"
  }
}
```

## Error Responses

### Authentication Errors
```json
{
  "error": "Authorization header missing",
  "code": 401
}
```

```json
{
  "error": "Invalid or expired token",
  "code": 401
}
```

### Validation Errors
```json
{
  "error": "Email and password are required",
  "code": 400
}
```

```json
{
  "error": "Invalid email format",
  "code": 400
}
```

### Business Logic Errors
```json
{
  "error": "Wallet already exists for this user",
  "code": 409
}
```

```json
{
  "error": "Wallet not found",
  "code": 404
}
```

## Security Features

1. **JWT Authentication**: Secure token-based authentication
2. **Password Hashing**: Passwords are hashed using PHP's `password_hash()`
3. **CORS Support**: Configurable cross-origin resource sharing
4. **Input Validation**: All inputs are validated and sanitized
5. **Error Handling**: Comprehensive error handling with appropriate HTTP status codes
6. **User Isolation**: Each user can only access their own wallet and data

## Architecture

The API follows a clean architecture with:

- **Controllers**: Handle HTTP requests and responses
- **Services**: Business logic and external API calls
- **Repositories**: Data access layer
- **Middleware**: Authentication and request processing
- **Config**: Environment and database configuration

## Database Schema

### Users Table
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Wallets Table
```sql
CREATE TABLE wallets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    address VARCHAR(255) NOT NULL UNIQUE,
    private_key TEXT NOT NULL,
    balance DECIMAL(20, 6) DEFAULT 0.000000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## Setup Instructions

1. **Install Dependencies**:
   ```bash
   composer install
   ```

2. **Configure Environment**:
   Copy `.env.example` to `.env` and configure your database settings.

3. **Initialize Database**:
   ```bash
   php setup_db.php
   ```

4. **Start Development Server**:
   ```bash
   php -S localhost:8000 -t src
   ```

5. **Test API**:
   ```bash
   php test_complete_api.php
   ```

## Example Usage (cURL)

### Register a User
```bash
curl -X POST http://localhost:8000/api/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### Create a Wallet
```bash
curl -X POST http://localhost:8000/api/create-wallet \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Get Balance
```bash
curl -X POST http://localhost:8000/api/balance \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## Development Notes

- The API is designed for development and testing
- In production, use HTTPS and secure JWT secrets
- Consider implementing rate limiting and additional security measures
- The TRON integration uses testnet by default
