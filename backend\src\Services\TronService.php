<?php

namespace Simbi\Tls\Services;

use IEXBase\TronAPI\Tron;
use Simbi\Tls\Config\Config;
use Exception;

class TronService
{
    private Tron $tron;
    private string $masterAddress;
    private string $masterPrivateKey;
    private string $usdtContract;

    public function __construct()
    {
        Config::load();
        
        $network = Config::get('TRON_NETWORK', 'shasta');
        $apiUrl = $network === 'mainnet' 
            ? 'https://api.trongrid.io'
            : 'https://api.shasta.trongrid.io';

        $fullNode = new \IEXBase\TronAPI\Provider\HttpProvider($apiUrl);
        $solidityNode = new \IEXBase\TronAPI\Provider\HttpProvider($apiUrl);
        $eventServer = new \IEXBase\TronAPI\Provider\HttpProvider($apiUrl);
        
        $this->tron = new Tron($fullNode, $solidityNode, $eventServer);
        
        $this->masterAddress = Config::get('MASTER_ADDRESS', '');
        $this->masterPrivateKey = Config::get('MASTER_PRIVATE_KEY', '');
        $this->usdtContract = Config::get('USDT_CONTRACT', '');
    }

    public function generateAddress(): array
    {
        try {
            $account = $this->tron->generateAddress();
            
            // Handle different return types from TronAPI
            if (is_array($account)) {
                return [
                    'address' => $account['base58'],
                    'privateKey' => $account['privateKey']
                ];
            } else {
                // If it's an object, try to get the properties
                return [
                    'address' => $account->getAddress(),
                    'privateKey' => $account->getPrivateKey()
                ];
            }
        } catch (Exception $e) {
            error_log("Failed to generate TRON address: " . $e->getMessage());
            throw new Exception("Failed to generate address: " . $e->getMessage());
        }
    }    public function getBalance(string $address): string
    {
        try {
            if (empty($this->usdtContract)) {
                // Return TRX balance if no USDT contract is configured
                $balance = $this->tron->getBalance($address);
                return (string)$balance;
            }

            // For USDT balance, we need to use contract calls differently
            // This is a simplified version - you may need to adjust based on the exact API
            $contract = $this->tron->contract($this->usdtContract);
            return $contract->balanceOf($address)->call();
        } catch (Exception $e) {
            error_log("Failed to get balance: " . $e->getMessage());
            return "0"; // Return 0 if balance check fails
        }
    }    public function sweepFunds(string $fromPrivateKey, string $fromAddress): array
    {
        try {
            if (empty($this->masterAddress)) {
                throw new Exception("Master address not configured");
            }

            $this->tron->setPrivateKey($fromPrivateKey);
            
            if (empty($this->usdtContract)) {
                // Sweep TRX instead of USDT
                $balance = $this->tron->getBalance($fromAddress);
                if ($balance > 0) {
                    $tx = $this->tron->sendTrx($this->masterAddress, $balance, $fromAddress);
                    return ['status' => 'swept', 'tx' => $tx, 'amount' => $balance];
                } else {
                    return ['status' => 'no funds to sweep', 'amount' => '0'];
                }
            }

            $contract = $this->tron->contract($this->usdtContract);
            $balance = $contract->balanceOf($fromAddress)->call();

            if ($balance > 0) {
                $tx = $contract->transfer($this->masterAddress, $balance)->send();
                return ['status' => 'swept', 'tx' => $tx, 'amount' => $balance];
            } else {
                return ['status' => 'no funds to sweep', 'amount' => '0'];
            }
        } catch (Exception $e) {
            error_log("Failed to sweep funds: " . $e->getMessage());
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }

    public function withdraw(string $toAddress, string $amount): array
    {
        try {
            if (empty($this->masterPrivateKey)) {
                throw new Exception("Master private key not configured");
            }

            $this->tron->setPrivateKey($this->masterPrivateKey);

            if (empty($this->usdtContract)) {
                // Send TRX instead of USDT
                $amountSun = bcmul($amount, '1000000'); // Convert TRX to SUN
                $tx = $this->tron->sendTrx($toAddress, $amountSun);
                return ['status' => 'withdrawn', 'tx' => $tx, 'amount' => $amount];
            }

            $contract = $this->tron->contract($this->usdtContract);
            $value = bcmul($amount, '1000000'); // USDT decimals
            $tx = $contract->transfer($toAddress, $value)->send();

            return ['status' => 'withdrawn', 'tx' => $tx, 'amount' => $amount];
        } catch (Exception $e) {
            error_log("Failed to withdraw: " . $e->getMessage());
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }

    public function validateAddress(string $address): bool
    {
        try {
            return $this->tron->isAddress($address);
        } catch (Exception $e) {
            return false;
        }
    }
}
