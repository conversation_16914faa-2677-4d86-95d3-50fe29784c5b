<?php
require_once 'config.php';
require_once 'api.php';

// Initialize session
FrontendConfig::initSession();

echo "Testing Dashboard Access...\n\n";

// Login with test credentials
$api = new APIWrapper();
$result = $api->login('<EMAIL>', 'password123');

if (isset($result['success']) && $result['success']) {
    echo "✅ Login successful!\n";
    
    // Store session data
    $_SESSION['user_id'] = $result['user']['id'];
    $_SESSION['email'] = $result['user']['email'];  
    $_SESSION['token'] = $result['token'];
    $_SESSION['is_admin'] = $result['user']['is_admin'] ?? false;
    
    echo "✅ Session stored successfully\n";
    echo "User ID: " . $_SESSION['user_id'] . "\n";
    echo "Email: " . $_SESSION['email'] . "\n";
    echo "Token: " . substr($_SESSION['token'], 0, 20) . "...\n";
    
    // Test authentication
    echo "\nTesting authentication...\n";
    $isAuth = FrontendConfig::isAuthenticated();
    echo "Is authenticated: " . ($isAuth ? "✅ YES" : "❌ NO") . "\n";
    
    if ($isAuth) {
        $user = FrontendConfig::getCurrentUser();
        echo "Current user data: " . json_encode($user, JSON_PRETTY_PRINT) . "\n";
        
        echo "\n✅ Dashboard should now be accessible!\n";
        echo "Visit: http://localhost:8889/user/dashboard.php\n";
    }
    
} else {
    echo "❌ Login failed: " . ($result['error'] ?? 'Unknown error') . "\n";
}
?>
