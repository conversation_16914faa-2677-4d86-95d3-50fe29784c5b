<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sticky Footer Menu Test - TRON Wallet</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <style>
        /* Test styles to make content scrollable */
        .test-content {
            padding: 20px;
            min-height: 150vh;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .demo-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1>TRON Wallet - Footer Menu Test</h1>
                <div class="header-actions">
                    <button class="btn btn-outline">Test Mode</button>
                </div>
            </div>
        </header>

        <!-- Navigation (will be hidden on mobile) -->
        <nav class="app-nav">
            <button class="nav-btn active" data-tab="dashboard">Dashboard</button>
            <button class="nav-btn" data-tab="wallet">Wallet</button>
            <button class="nav-btn" data-tab="transactions">Transactions</button>
            <button class="nav-btn" data-tab="deposit">Deposit</button>
        </nav>

        <!-- Main Content -->
        <main class="app-main">
            <div class="test-content">
                <div class="demo-info">
                    <h3>📱 Sticky Footer Menu Demo</h3>
                    <p><strong>Desktop:</strong> You'll see the horizontal navigation bar above</p>
                    <p><strong>Mobile (768px and below):</strong> The navigation bar will be hidden and a sticky footer menu will appear at the bottom</p>
                    <p><strong>To test:</strong> Resize your browser window to mobile width or use developer tools mobile view</p>
                </div>

                <!-- Dashboard Tab -->
                <div id="dashboard" class="tab-content active">
                    <div class="test-section">
                        <h2>🏠 Dashboard Tab</h2>
                        <p>This is the dashboard content. The footer menu should show this tab as active.</p>
                        <p>Scroll down to test the sticky footer behavior...</p>
                    </div>
                </div>

                <!-- Wallet Tab -->
                <div id="wallet" class="tab-content" style="display: none;">
                    <div class="test-section">
                        <h2>💼 Wallet Tab</h2>
                        <p>This is the wallet content. Click the wallet icon in the footer to see this tab.</p>
                    </div>
                </div>

                <!-- Transactions Tab -->
                <div id="transactions" class="tab-content" style="display: none;">
                    <div class="test-section">
                        <h2>📋 Transactions Tab</h2>
                        <p>This is the transactions content. Click the transactions icon in the footer to see this tab.</p>
                    </div>
                </div>

                <!-- Deposit Tab -->
                <div id="deposit" class="tab-content" style="display: none;">
                    <div class="test-section">
                        <h2>📥 Deposit Tab</h2>
                        <p>This is the deposit content. Click the deposit icon in the footer to see this tab.</p>
                    </div>
                </div>

                <!-- More content to test scrolling -->
                <div class="test-section">
                    <h3>Test Content 1</h3>
                    <p>This content helps test the scrolling behavior with the sticky footer.</p>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                </div>

                <div class="test-section">
                    <h3>Test Content 2</h3>
                    <p>More test content to ensure there's enough scroll height.</p>
                    <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                </div>

                <div class="test-section">
                    <h3>Test Content 3</h3>
                    <p>Additional test content for scrolling behavior verification.</p>
                    <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
                </div>

                <div class="test-section">
                    <h3>Bottom Content</h3>
                    <p>This is the bottom of the content. The footer should stay visible when you scroll here.</p>
                    <p style="color: #667eea; font-weight: bold;">✅ If you can see this text and the footer menu simultaneously on mobile, the implementation is working correctly!</p>
                </div>
            </div>
        </main>
    </div>

    <!-- Sticky Footer Menu -->
    <footer class="footer-menu">
        <div class="footer-nav">
            <button class="footer-btn active" data-tab="dashboard">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" stroke="currentColor" stroke-width="2" fill="currentColor"/>
                </svg>
                <span>Dashboard</span>
            </button>
            <button class="footer-btn" data-tab="wallet">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 18v1a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2" fill="none"/>
                    <path d="M16 8h4a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-4" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="16" cy="12" r="1" fill="currentColor"/>
                </svg>
                <span>Wallet</span>
            </button>
            <button class="footer-btn" data-tab="transactions">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <rect x="8" y="2" width="8" height="4" rx="1" ry="1" stroke="currentColor" stroke-width="2" fill="none"/>
                    <path d="M9 12h6" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 16h6" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>Transactions</span>
            </button>
            <button class="footer-btn" data-tab="deposit">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2v20" stroke="currentColor" stroke-width="2"/>
                    <path d="M17 7l-5-5-5 5" stroke="currentColor" stroke-width="2"/>
                    <path d="M3 17h18" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>Deposit</span>
            </button>
            <button class="footer-btn" onclick="alert('Profile clicked!')">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" fill="none"/>
                </svg>
                <span>Profile</span>
            </button>
        </div>
    </footer>

    <script>
        // Simple navigation for testing
        document.addEventListener('DOMContentLoaded', function() {
            setupTestNavigation();
        });

        function setupTestNavigation() {
            // Tab navigation for both regular nav and footer nav
            const navButtons = document.querySelectorAll('.nav-btn, .footer-btn[data-tab]');
            
            navButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const tabId = this.dataset.tab;
                    if (tabId) {
                        showTab(tabId);
                        
                        // Update active state for both nav types
                        document.querySelectorAll('.nav-btn').forEach(b => b.classList.remove('active'));
                        document.querySelectorAll('.footer-btn[data-tab]').forEach(b => b.classList.remove('active'));
                        
                        // Set active state for clicked button type
                        if (this.classList.contains('nav-btn')) {
                            this.classList.add('active');
                            // Also update corresponding footer button
                            const footerBtn = document.querySelector(`.footer-btn[data-tab="${tabId}"]`);
                            if (footerBtn) footerBtn.classList.add('active');
                        } else if (this.classList.contains('footer-btn')) {
                            this.classList.add('active');
                            // Also update corresponding nav button
                            const navBtn = document.querySelector(`.nav-btn[data-tab="${tabId}"]`);
                            if (navBtn) navBtn.classList.add('active');
                        }
                    }
                });
            });
        }

        function showTab(tabId) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });
            
            // Show selected tab
            const selectedTab = document.getElementById(tabId);
            if (selectedTab) {
                selectedTab.style.display = 'block';
            }
        }
    </script>
</body>
</html>
