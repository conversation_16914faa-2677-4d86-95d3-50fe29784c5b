<?php
// Complete System Verification with MySQL Backend
require_once 'config.php';
require_once 'api.php';

echo "=== TLS Crypto Wallet - Complete System Verification ===\n";
echo "Backend: MySQL Database\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

$totalTests = 0;
$passedTests = 0;

function runTest($testName, $callback) {
    global $totalTests, $passedTests;
    $totalTests++;
    echo "[$totalTests] Testing: $testName\n";
    
    try {
        $result = $callback();
        if ($result) {
            echo "✅ PASSED: $testName\n";
            $passedTests++;
        } else {
            echo "❌ FAILED: $testName\n";
        }
    } catch (Exception $e) {
        echo "❌ ERROR: $testName - " . $e->getMessage() . "\n";
    }
    echo "\n";
}

// Test 1: Database Connection
runTest("MySQL Database Connection", function() {
    $pdo = new PDO("mysql:host=localhost;dbname=tlssc;charset=utf8mb4", "root", "");
    return $pdo !== null;
});

// Test 2: API Registration
runTest("User Registration via API", function() {
    $api = new APIWrapper();
    $testEmail = 'verify_' . time() . '@example.com';
    $result = $api->register($testEmail, 'testpass123');
    return isset($result['success']) && $result['success'];
});

// Test 3: API Login
runTest("User Login via API", function() {
    $api = new APIWrapper();
    $testEmail = 'verify_login_' . time() . '@example.com';
    
    // Register first
    $regResult = $api->register($testEmail, 'testpass123');
    if (!$regResult['success']) return false;
    
    // Then login
    $loginResult = $api->login($testEmail, 'testpass123');
    return isset($loginResult['success']) && $loginResult['success'];
});

// Test 4: Frontend AJAX Registration
runTest("Frontend AJAX Registration", function() {
    $testEmail = 'ajax_test_' . time() . '@example.com';
    
    $postData = json_encode([
        'action' => 'register',
        'email' => $testEmail,
        'password' => 'testpass123'
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $postData
        ]
    ]);
    
    $response = file_get_contents('http://localhost:8080/ajax.php', false, $context);
    $result = json_decode($response, true);
    
    return isset($result['success']) && $result['success'];
});

// Test 5: Frontend AJAX Login
runTest("Frontend AJAX Login", function() {
    $testEmail = 'ajax_login_' . time() . '@example.com';
    
    // Register first via AJAX
    $regData = json_encode([
        'action' => 'register',
        'email' => $testEmail,
        'password' => 'testpass123'
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $regData
        ]
    ]);
    
    file_get_contents('http://localhost:8080/ajax.php', false, $context);
    
    // Now test login
    $loginData = json_encode([
        'action' => 'login',
        'email' => $testEmail,
        'password' => 'testpass123'
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $loginData
        ]
    ]);
    
    $response = file_get_contents('http://localhost:8080/ajax.php', false, $context);
    $result = json_decode($response, true);
    
    return isset($result['success']) && $result['success'];
});

// Test 6: Database Tables Structure
runTest("Database Tables Structure", function() {
    $pdo = new PDO("mysql:host=localhost;dbname=tlssc;charset=utf8mb4", "root", "");
    
    $expectedTables = ['users', 'wallets', 'transactions', 'password_resets', 'admin_logs'];
    $actualTables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($expectedTables as $table) {
        if (!in_array($table, $actualTables)) {
            return false;
        }
    }
    return true;
});

// Test 7: Frontend Pages Accessibility
runTest("Frontend Pages Accessibility", function() {
    $pages = [
        'http://localhost:8080/index.php',
        'http://localhost:8080/api-test.html',
        'http://localhost:8080/test.html'
    ];
    
    foreach ($pages as $page) {
        $headers = @get_headers($page);
        if (!$headers || strpos($headers[0], '200') === false) {
            return false;
        }
    }
    return true;
});

// Test 8: Backend API Endpoints
runTest("Backend API Endpoints", function() {
    $endpoints = [
        'http://localhost:8000/api/register',
        'http://localhost:8000/api/login'
    ];
    
    foreach ($endpoints as $endpoint) {
        $context = stream_context_create([
            'http' => [
                'method' => 'OPTIONS',
                'ignore_errors' => true
            ]
        ]);
        
        $response = @file_get_contents($endpoint, false, $context);
        if ($response === false) {
            return false;
        }
    }
    return true;
});

// Test 9: User Data Persistence
runTest("User Data Persistence", function() {
    $pdo = new PDO("mysql:host=localhost;dbname=tlssc;charset=utf8mb4", "root", "");
    
    $api = new APIWrapper();
    $testEmail = 'persistence_' . time() . '@example.com';
    
    // Register user
    $result = $api->register($testEmail, 'testpass123');
    if (!$result['success']) return false;
    
    // Check if user exists in database
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
    $stmt->execute([$testEmail]);
    $count = $stmt->fetchColumn();
    
    return $count > 0;
});

// Test 10: Session Management
runTest("Session Management", function() {
    // Test session initialization
    FrontendConfig::initSession();
    
    // Test session functions
    $isAuth = FrontendConfig::isAuthenticated();
    $isAdmin = FrontendConfig::isAdmin();
    
    // These should return false for fresh session
    return $isAuth === false && $isAdmin === false;
});

// Final Results
echo "=== VERIFICATION COMPLETE ===\n";
echo "Tests Passed: $passedTests / $totalTests\n";
echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

if ($passedTests === $totalTests) {
    echo "🎉 ALL TESTS PASSED! System is fully operational.\n";
    echo "✅ MySQL Backend: Working perfectly\n";
    echo "✅ Frontend Interface: All pages accessible\n";
    echo "✅ API Integration: Frontend-backend communication verified\n";
    echo "✅ Authentication: Registration and login functional\n";
    echo "✅ Database: Data persistence confirmed\n";
    echo "✅ Session Management: Security controls active\n\n";
    echo "🚀 Ready for production deployment!\n";
} else {
    echo "⚠️  Some tests failed. Please review the issues above.\n";
    $failedTests = $totalTests - $passedTests;
    echo "❌ Failed Tests: $failedTests\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "TLS Crypto Wallet - System Status: " . ($passedTests === $totalTests ? "OPERATIONAL" : "ISSUES DETECTED") . "\n";
echo str_repeat("=", 50) . "\n";
?>
