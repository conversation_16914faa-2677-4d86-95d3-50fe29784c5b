<?php
require_once 'config.php';
require_once 'api.php';

// Initialize session
FrontendConfig::initSession();

echo "Testing user registration and login...\n\n";

$api = new APIWrapper();
$testEmail = '<EMAIL>';
$testPassword = 'password123';

// Test registration
echo "1. Registering user: $testEmail\n";
$registerResult = $api->register($testEmail, $testPassword);

if (isset($registerResult['success']) && $registerResult['success']) {
    echo "✅ Registration successful!\n";
    echo "User ID: " . $registerResult['user']['id'] . "\n";
    echo "Token: " . substr($registerResult['token'], 0, 20) . "...\n\n";
    
    // Store session data
    $_SESSION['user_id'] = $registerResult['user']['id'];
    $_SESSION['email'] = $registerResult['user']['email'];
    $_SESSION['token'] = $registerResult['token'];
    $_SESSION['is_admin'] = $registerResult['user']['is_admin'] ?? false;
    
    echo "2. Testing authentication check...\n";
    $isAuth = FrontendConfig::isAuthenticated();
    echo "Is authenticated: " . ($isAuth ? "✅ YES" : "❌ NO") . "\n";
    
    if ($isAuth) {
        $user = FrontendConfig::getCurrentUser();
        echo "Current user: " . print_r($user, true) . "\n";
        echo "\n✅ Authentication flow is working!\n";
        echo "You can now login with:\n";
        echo "Email: $testEmail\n";
        echo "Password: $testPassword\n";
    }
    
} else {
    echo "❌ Registration failed: " . ($registerResult['error'] ?? 'Unknown error') . "\n";
    echo "Full response: " . print_r($registerResult, true) . "\n";
}
?>
