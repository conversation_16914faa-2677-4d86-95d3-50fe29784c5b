<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Flow Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 20px; margin: 5px; }
        input { padding: 8px; margin: 5px; width: 200px; }
        #result { margin-top: 20px; padding: 10px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>Login Flow Test</h1>
    
    <div class="test-section">
        <h3>1. Test Login via AJAX</h3>
        <input type="email" id="testEmail" placeholder="Email" value="<EMAIL>">
        <input type="password" id="testPassword" placeholder="Password" value="testflow123">
        <button onclick="testLogin()">Test Login</button>
    </div>
    
    <div class="test-section">
        <h3>2. Test Authentication Status</h3>
        <button onclick="checkAuthStatus()">Check Auth Status</button>
    </div>
    
    <div class="test-section">
        <h3>3. Test Dashboard Access</h3>
        <button onclick="testDashboard()">Test Dashboard</button>
    </div>
    
    <div class="test-section">
        <h3>4. Test Logout</h3>
        <button onclick="testLogout()">Test Logout</button>
    </div>
    
    <div id="result"></div>

    <script>
        const resultDiv = document.getElementById('result');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            resultDiv.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }
        
        async function testLogin() {
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            
            log('Testing login...', 'info');
            
            try {
                const response = await fetch('ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log('✅ Login successful!', 'success');
                    log(`User ID: ${result.user.id}`, 'info');
                    log(`Email: ${result.user.email}`, 'info');
                    log(`Is Admin: ${result.user.is_admin ? 'Yes' : 'No'}`, 'info');
                } else {
                    log(`❌ Login failed: ${result.error}`, 'error');
                }
            } catch (error) {
                log(`❌ Login error: ${error.message}`, 'error');
            }
        }
        
        async function checkAuthStatus() {
            log('Checking authentication status...', 'info');
            
            try {
                const response = await fetch('ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'get_user_profile'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log('✅ User is authenticated', 'success');
                    log(`Profile: ${JSON.stringify(result.user, null, 2)}`, 'info');
                } else {
                    log(`❌ Authentication check failed: ${result.error}`, 'error');
                }
            } catch (error) {
                log(`❌ Auth check error: ${error.message}`, 'error');
            }
        }
        
        async function testDashboard() {
            log('Testing dashboard access...', 'info');
            
            try {
                const response = await fetch('user/dashboard.php');
                const text = await response.text();
                
                if (response.ok && text.includes('Dashboard')) {
                    log('✅ Dashboard accessible', 'success');
                } else if (response.redirected || text.includes('login')) {
                    log('❌ Dashboard redirected to login (not authenticated)', 'error');
                } else {
                    log(`❌ Dashboard access failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Dashboard test error: ${error.message}`, 'error');
            }
        }
        
        async function testLogout() {
            log('Testing logout...', 'info');
            
            try {
                const response = await fetch('index.php?logout=1');
                
                if (response.ok) {
                    log('✅ Logout request successful', 'success');
                    
                    // Test if still authenticated
                    setTimeout(async () => {
                        try {
                            const authResponse = await fetch('ajax.php', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                    action: 'get_user_profile'
                                })
                            });
                            
                            const authResult = await authResponse.json();
                            
                            if (authResult.success) {
                                log('❌ Still authenticated after logout', 'error');
                            } else {
                                log('✅ Successfully logged out', 'success');
                            }
                        } catch (error) {
                            log('✅ Successfully logged out (auth check failed)', 'success');
                        }
                    }, 1000);
                } else {
                    log(`❌ Logout failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Logout error: ${error.message}`, 'error');
            }
        }
        
        // Clear results on page load
        log('Login Flow Test Ready', 'info');
    </script>
</body>
</html>
