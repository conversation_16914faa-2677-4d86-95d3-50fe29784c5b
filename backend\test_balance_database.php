<?php
/**
 * Test script to verify balance endpoint pulls from database instead of TronWeb
 */

require_once 'vendor/autoload.php';

use Simbi\Tls\Config\Config;
use Simbi\Tls\Config\Database;

// Load configuration
Config::load();

$baseUrl = 'http://localhost:8000';

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        $headers[] = 'Content-Type: application/json';
    }
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return ['code' => $httpCode, 'body' => json_decode($response, true)];
}

echo "🔍 Testing Balance Endpoint with Database Approach\n";
echo "================================================\n\n";

// Step 1: Register a test user
echo "1. Registering test user...\n";
$registerResponse = makeRequest("$baseUrl/api/register", 'POST', [
    'email' => 'balance_test_' . time() . '@example.com',
    'password' => 'testpassword123'
]);

if ($registerResponse['code'] !== 200 || !$registerResponse['body']['success']) {
    echo "❌ Registration failed\n";
    var_dump($registerResponse);
    exit(1);
}

$token = $registerResponse['body']['token'];
echo "✅ User registered successfully\n\n";

// Step 2: Create a wallet or get existing wallet
echo "2. Creating wallet...\n";
$walletResponse = makeRequest("$baseUrl/api/create-wallet", 'POST', null, [
    "Authorization: Bearer $token"
]);

if ($walletResponse['code'] === 409) {
    // Wallet already exists, get balance to find address
    echo "⚠️  Wallet already exists, getting existing wallet info...\n";
    $balanceResponse = makeRequest("$baseUrl/api/balance", 'POST', null, [
        "Authorization: Bearer $token"
    ]);
    
    if ($balanceResponse['code'] !== 200 || !$balanceResponse['body']['success']) {
        echo "❌ Failed to get existing wallet info\n";
        var_dump($balanceResponse);
        exit(1);
    }
    
    $walletAddress = $balanceResponse['body']['address'];
    echo "✅ Using existing wallet: $walletAddress\n\n";
} elseif ($walletResponse['code'] !== 200 || !$walletResponse['body']['success']) {
    echo "❌ Wallet creation failed\n";
    var_dump($walletResponse);
    exit(1);
} else {
    $walletAddress = $walletResponse['body']['address'];
    echo "✅ Wallet created: $walletAddress\n\n";
}

// Step 3: Check initial balance (should be 0.000000 from database)
echo "3. Checking initial balance from database...\n";
$balanceResponse = makeRequest("$baseUrl/api/balance", 'POST', null, [
    "Authorization: Bearer $token"
]);

if ($balanceResponse['code'] !== 200 || !$balanceResponse['body']['success']) {
    echo "❌ Balance check failed\n";
    var_dump($balanceResponse);
    exit(1);
}

echo "✅ Balance retrieved from database:\n";
echo "   - Address: " . $balanceResponse['body']['address'] . "\n";
echo "   - Balance: " . $balanceResponse['body']['balance'] . "\n";
echo "   - Formatted: " . $balanceResponse['body']['balance_formatted'] . "\n\n";

// Step 4: Manually update balance in database to test database approach
echo "4. Manually updating balance in database to test database approach...\n";

try {
    $pdo = Database::getConnection();
    
    if (!$pdo) {
        echo "❌ Failed to connect to database\n";
        exit(1);
    }
    
    // Find the wallet ID
    $stmt = $pdo->prepare("SELECT id FROM wallets WHERE address = ?");
    $stmt->execute([$walletAddress]);
    $wallet = $stmt->fetch();
    
    if (!$wallet) {
        echo "❌ Wallet not found in database\n";
        exit(1);
    }
    
    // Update balance to 123.456789
    $testBalance = 123.456789;
    $stmt = $pdo->prepare("UPDATE wallets SET balance = ? WHERE id = ?");
    $stmt->execute([$testBalance, $wallet['id']]);
    
    echo "✅ Database balance updated to: $testBalance\n\n";
    
} catch (Exception $e) {
    echo "❌ Database update failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 5: Check balance again (should show the database value)
echo "5. Checking balance again (should show database value)...\n";
$balanceResponse2 = makeRequest("$baseUrl/api/balance", 'POST', null, [
    "Authorization: Bearer $token"
]);

if ($balanceResponse2['code'] !== 200 || !$balanceResponse2['body']['success']) {
    echo "❌ Second balance check failed\n";
    var_dump($balanceResponse2);
    exit(1);
}

echo "✅ Updated balance retrieved from database:\n";
echo "   - Address: " . $balanceResponse2['body']['address'] . "\n";
echo "   - Balance: " . $balanceResponse2['body']['balance'] . "\n";
echo "   - Formatted: " . $balanceResponse2['body']['balance_formatted'] . "\n\n";

// Verify the balance matches our database update
if ((float)$balanceResponse2['body']['balance'] === 123.456789) {
    echo "🎉 SUCCESS: Balance endpoint is correctly reading from database!\n";
    echo "   - Expected: 123.456789\n";
    echo "   - Actual: " . $balanceResponse2['body']['balance'] . "\n";
} else {
    echo "❌ FAILED: Balance does not match expected database value\n";
    echo "   - Expected: 123.456789\n";
    echo "   - Actual: " . $balanceResponse2['body']['balance'] . "\n";
    exit(1);
}

echo "\n✅ All tests passed! Balance endpoint successfully modified to use database.\n";
