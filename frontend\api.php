<?php
require_once 'config.php';

class APIWrapper {
    private $baseUrl;
    private $token;

    public function __construct($baseUrl = null) {
        $this->baseUrl = rtrim($baseUrl ?: FrontendConfig::getApiUrl(), '/');
        $this->token = $_SESSION['token'] ?? null;
    }

    public function setToken($token) {
        $this->token = $token;
    }

    private function makeRequest($endpoint, $method = 'GET', $data = null, $useAuth = true) {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        // Set headers
        $headers = ['Content-Type: application/json'];
        if ($useAuth && $this->token) {
            $headers[] = 'Authorization: Bearer ' . $this->token;
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        // Set method and data
        switch (strtoupper($method)) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_error($ch)) {
            curl_close($ch);
            return ['error' => 'Connection error: ' . curl_error($ch)];
        }
        
        curl_close($ch);

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['error' => 'Invalid JSON response'];
        }

        return $decodedResponse;
    }

    // Authentication methods
    public function register($email, $password) {
        return $this->makeRequest('/api/register', 'POST', [
            'email' => $email,
            'password' => $password
        ], false);
    }

    public function login($email, $password) {
        return $this->makeRequest('/api/login', 'POST', [
            'email' => $email,
            'password' => $password
        ], false);
    }

    public function passwordResetRequest($email) {
        return $this->makeRequest('/api/password-reset-request', 'POST', [
            'email' => $email
        ], false);
    }

    public function passwordReset($token, $newPassword) {
        return $this->makeRequest('/api/password-reset', 'POST', [
            'token' => $token,
            'new_password' => $newPassword
        ], false);
    }

    // User methods
    public function getUserProfile() {
        return $this->makeRequest('/api/me', 'GET');
    }

    public function changePassword($currentPassword, $newPassword) {
        return $this->makeRequest('/api/change-password', 'POST', [
            'current_password' => $currentPassword,
            'new_password' => $newPassword
        ]);
    }

    // Wallet methods
    public function createWallet() {
        return $this->makeRequest('/api/create-wallet', 'POST');
    }

    public function getBalance() {
        return $this->makeRequest('/api/balance', 'POST');
    }

    public function withdraw($to, $amount) {
        return $this->makeRequest('/api/withdraw', 'POST', [
            'to' => $to,
            'amount' => $amount
        ]);
    }

    public function sweepFunds() {
        return $this->makeRequest('/api/sweep-funds', 'POST');
    }

    // Transaction methods
    public function getTransactions($limit = 10, $offset = 0, $type = null, $status = null) {
        $params = ['limit' => $limit, 'offset' => $offset];
        if ($type) $params['type'] = $type;
        if ($status) $params['status'] = $status;
        
        $query = http_build_query($params);
        return $this->makeRequest('/api/transactions?' . $query, 'GET');
    }

    public function recordDeposit($walletAddress, $transactionHash, $amount, $fromAddress) {
        return $this->makeRequest('/api/transactions/deposit', 'POST', [
            'wallet_address' => $walletAddress,
            'transaction_hash' => $transactionHash,
            'amount' => $amount,
            'from_address' => $fromAddress
        ]);
    }

    public function getTransactionStatistics() {
        return $this->makeRequest('/api/transactions/statistics', 'GET');
    }

    public function getTransactionDetails($id) {
        return $this->makeRequest('/api/transactions/' . $id, 'GET');
    }

    public function getWalletTransactions($walletId, $limit = 10, $offset = 0) {
        $params = ['limit' => $limit, 'offset' => $offset];
        $query = http_build_query($params);
        return $this->makeRequest('/api/wallets/' . $walletId . '/transactions?' . $query, 'GET');
    }

    // Admin methods
    public function getSystemStatistics() {
        return $this->makeRequest('/api/admin/statistics', 'GET');
    }

    public function getUserList($limit = 20, $offset = 0, $status = null) {
        $params = ['limit' => $limit, 'offset' => $offset];
        if ($status) $params['status'] = $status;
        
        $query = http_build_query($params);
        return $this->makeRequest('/api/admin/users?' . $query, 'GET');
    }

    public function updateUserStatus($userId, $isActive) {
        return $this->makeRequest('/api/admin/users/' . $userId . '/status', 'PUT', [
            'is_active' => $isActive
        ]);
    }

    public function promoteUser($userId) {
        return $this->makeRequest('/api/admin/users/' . $userId . '/promote', 'POST');
    }

    public function getActivityLogs($limit = 50, $offset = 0) {
        $params = ['limit' => $limit, 'offset' => $offset];
        $query = http_build_query($params);
        return $this->makeRequest('/api/admin/logs?' . $query, 'GET');
    }

    public function getAdminTransactionStatistics() {
        return $this->makeRequest('/api/admin/transactions/statistics', 'GET');
    }

    public function getSystemHealth() {
        return $this->makeRequest('/api/admin/health', 'GET');
    }
}

// Handle direct API calls via query string (for frontend JavaScript)
if (isset($_GET['action'])) {
    FrontendConfig::initSession();
    header('Content-Type: application/json');
    
    $action = $_GET['action'];
    $api = new APIWrapper();
    
    // Get request data for POST requests
    $data = null;
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
    }
    
    try {
        switch ($action) {
            case 'get_balance':
                $result = $api->getBalance();
                break;
                
            case 'get_transaction_statistics':
                $result = $api->getTransactionStatistics();
                break;
                
            case 'get_transactions':
                $limit = $_GET['limit'] ?? $data['limit'] ?? 10;
                $page = $_GET['page'] ?? $data['page'] ?? 0;
                $offset = $page * $limit;
                $type = $_GET['type'] ?? $data['type'] ?? null;
                $status = $_GET['status'] ?? $data['status'] ?? null;
                $result = $api->getTransactions($limit, $offset, $type, $status);
                break;
                
            case 'get_wallet':
                // For wallet data, we can get it from balance response
                $result = $api->getBalance();
                break;
                
            case 'create_wallet':
                $result = $api->createWallet();
                break;
                
            case 'withdraw':
                if ($data && isset($data['to'], $data['amount'])) {
                    $result = $api->withdraw($data['to'], $data['amount']);
                } else {
                    $result = ['error' => 'Missing required parameters: to, amount'];
                }
                break;
                
            case 'sweep_funds':
                $result = $api->sweepFunds();
                break;
                
            case 'record_deposit':
                if ($data && isset($data['wallet_address'], $data['transaction_hash'], $data['amount'], $data['from_address'])) {
                    $result = $api->recordDeposit($data['wallet_address'], $data['transaction_hash'], $data['amount'], $data['from_address']);
                } else {
                    $result = ['error' => 'Missing required parameters for deposit'];
                }
                break;
                
            default:
                $result = ['error' => 'Unknown action: ' . $action];
        }
        
        echo json_encode($result);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Internal server error: ' . $e->getMessage()]);
    }
    
    exit;
}
?>
