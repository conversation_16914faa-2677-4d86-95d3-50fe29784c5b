# TRON Wallet Service API - Advanced Features

A comprehensive, enterprise-grade TRON wallet service with advanced features including user authentication, wallet management, transaction tracking, admin dashboard, and automated sweeping capabilities.

## 🚀 Key Features

### Core Features
- **User Authentication** - JWT-based secure authentication system
- **Wallet Management** - Create and manage TRON wallets  
- **Transaction Tracking** - Complete transaction history and monitoring
- **Fund Sweeping** - Automated fund collection to master wallets
- **Balance Management** - Real-time balance tracking and updates

### Advanced Features (NEW)
- **Password Reset** - Email-based password reset with secure tokens
- **Transaction Analytics** - Detailed statistics and filtering
- **Admin Dashboard** - Complete administrative interface
- **Activity Logging** - Comprehensive audit trails
- **System Monitoring** - Health checks and performance metrics

## 🚀 Quick Start

### Prerequisites
- PHP 8.2+
- MySQL 5.7+
- Composer

### Installation

1. **Clone and setup**:
   ```powershell
   git clone <repository-url>
   cd tls
   composer install
   ```

2. **Configure environment**:
   ```powershell
   Copy-Item .env.example .env
   # Edit .env with your database credentials
   ```

3. **Initialize database**:
   ```powershell
   php setup_db.php
   ```

4. **Start development server**:
   ```powershell
   php -S localhost:8000 -t src
   ```

5. **Test the API**:
   ```powershell
   php test_complete_api.php
   ```

## 📚 API Documentation

### Base URL
```
http://localhost:8000
```

### Authentication
All protected endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

### Endpoints

#### Public Endpoints
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `POST /api/password-reset-request` - Request password reset
- `POST /api/password-reset` - Reset password with token

#### Authentication Endpoints
- `GET /api/me` - Get user profile
- `POST /api/change-password` - Change current password

#### Wallet Management
- `POST /api/create-wallet` - Create TRON wallet
- `POST /api/balance` - Get wallet balance
- `POST /api/withdraw` - Withdraw funds
- `POST /api/sweep-funds` - Sweep all funds

#### Transaction Management
- `GET /api/transactions` - Get transaction history (with filters)
- `GET /api/transactions/{id}` - Get specific transaction details
- `POST /api/transactions/deposit` - Record a deposit transaction
- `GET /api/transactions/statistics` - Get transaction statistics
- `GET /api/wallets/{id}/transactions` - Get transactions for specific wallet

#### Admin Endpoints (Admin Access Required)
- `GET /api/admin/statistics` - Get system-wide statistics
- `GET /api/admin/users` - List all users (with filters)
- `PUT /api/admin/users/{id}/status` - Update user status (active/inactive)
- `POST /api/admin/users/{id}/promote` - Promote user to admin
- `GET /api/admin/logs` - Get admin activity logs
- `GET /api/admin/transactions/statistics` - Get system transaction statistics
- `GET /api/admin/health` - Get system health metrics

### Example Usage

#### Register a User
```bash
curl -X POST http://localhost:8000/api/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","confirm_password":"password123"}'
```

#### Login
```bash
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

#### Request Password Reset
```bash
curl -X POST http://localhost:8000/api/password-reset-request \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

#### Create a Wallet
```bash
curl -X POST http://localhost:8000/api/create-wallet \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### Get Transaction History
```bash
curl -X GET "http://localhost:8000/api/transactions?limit=10&offset=0&type=deposit&status=confirmed" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### Record a Deposit
```bash
curl -X POST http://localhost:8000/api/transactions/deposit \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "wallet_address":"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
    "transaction_hash":"abcd1234...",
    "amount":"10.500000",
    "from_address":"TFromAddress123"  }'
```

#### Admin: Get System Statistics
```bash
curl -X GET http://localhost:8000/api/admin/statistics \
  -H "Authorization: Bearer ADMIN_TOKEN_HERE"
```

#### Check Balance
```bash
curl -X POST http://localhost:8000/api/balance \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🛠️ Management Tools

### CLI Management Tool
Use the included CLI tool for common operations:

```powershell
# Setup database
php manage.php setup

# Create a user
php manage.php user:create <EMAIL> password123

# List users
php manage.php user:list

# Show system statistics
php manage.php stats

# Run system tests
php manage.php test
```

## 🏗️ Project Structure

```
tls/
├── src/                          # Application source code
│   ├── Config/                   # Configuration management
│   │   ├── Config.php           # Environment configuration
│   │   └── Database.php         # Database connection
│   ├── Controllers/              # HTTP request handlers
│   │   ├── AuthController.php   # Authentication endpoints
│   │   └── WalletController.php # Wallet management endpoints
│   ├── Middleware/               # Request middleware
│   │   └── AuthMiddleware.php   # JWT authentication middleware
│   ├── Repositories/             # Data access layer
│   │   ├── UserRepository.php   # User data operations
│   │   └── WalletRepository.php # Wallet data operations
│   ├── Services/                 # Business logic layer
│   │   ├── AuthService.php      # Authentication logic
│   │   ├── Router.php           # HTTP routing
│   │   └── TronService.php      # TRON blockchain integration
│   └── index.php                # Main application entry point
├── tests/                        # Test files
├── vendor/                       # Composer dependencies
├── .env.example                  # Environment configuration template
├── .htaccess                     # Apache rewrite rules
├── composer.json                 # PHP dependencies
├── database.sql                  # Database schema
├── manage.php                    # CLI management tool
└── README.md                     # This file
```

## 🗄️ Database Schema

The database includes comprehensive tables for all advanced features:

### Core Tables

#### Users Table
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    is_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    password_reset_token VARCHAR(255) NULL,
    password_reset_expires TIMESTAMP NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### Wallets Table
```sql
CREATE TABLE wallets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    address VARCHAR(255) NOT NULL UNIQUE,
    private_key TEXT NOT NULL,
    balance DECIMAL(20, 6) DEFAULT 0.000000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### Transactions Table
```sql
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    wallet_id INT NOT NULL,
    transaction_hash VARCHAR(255) NOT NULL,
    type ENUM('deposit', 'withdrawal', 'sweep', 'transfer') NOT NULL,
    amount DECIMAL(20, 6) NOT NULL,
    fee DECIMAL(20, 6) DEFAULT 0.000000,
    from_address VARCHAR(255) NULL,
    to_address VARCHAR(255) NULL,
    status ENUM('pending', 'confirmed', 'failed') DEFAULT 'pending',
    block_number BIGINT NULL,    confirmations INT DEFAULT 0,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (wallet_id) REFERENCES wallets(id) ON DELETE CASCADE
);
```

### Advanced Feature Tables

#### Admin Activity Logs
```sql
CREATE TABLE admin_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50) NOT NULL,
    target_id VARCHAR(50) NULL,
    details JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### Password Reset Tokens
```sql
CREATE TABLE password_resets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    ip_address VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## 🧪 Testing

### Automated Tests
Run the complete test suite:
```powershell
php test_complete_api.php
```

### Manual Testing
1. Start the development server
2. Use the provided test scripts
3. Use curl or Postman for API testing

## 🚀 Production Deployment

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed production deployment instructions.

### Key Production Considerations
- Use HTTPS in production
- Set strong JWT secrets
- Configure proper CORS origins
- Enable rate limiting
- Use production database credentials
- Set up proper logging and monitoring

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Database
DB_HOST=localhost
DB_NAME=tlssc
DB_USER=root
DB_PASS=

# JWT Secret (256+ bits for production)
JWT_SECRET=your-super-secure-jwt-secret

# API Environment
API_ENV=development
API_DEBUG=true

# TRON Network
TRON_NETWORK=testnet
USDT_CONTRACT=TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t

# Security
CORS_ORIGINS=*
RATE_LIMIT_ENABLED=false
```

## 📊 API Response Examples

### Successful Registration
```json
{
  "success": true,
  "message": "User registered successfully",
  "user": {
    "id": 1,
    "email": "<EMAIL>"
  },
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### Wallet Creation
```json
{
  "success": true,
  "address": "41935c7638b684520d1caa5d22d5cd7492cab38df6",
  "message": "Wallet created successfully"
}
```

### Balance Check
```json
{
  "success": true,
  "address": "41935c7638b684520d1caa5d22d5cd7492cab38df6",
  "balance": "1000000",
  "balance_formatted": "1.000000"
}
```

## 🛡️ Security Features

- **JWT Authentication**: Stateless, secure token-based authentication
- **Password Hashing**: bcrypt hashing for password storage
- **Input Validation**: Comprehensive input validation and sanitization
- **SQL Injection Protection**: PDO prepared statements
- **XSS Protection**: JSON-only responses
- **CORS Configuration**: Configurable cross-origin policies
- **Error Handling**: Secure error messages without information leakage

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
1. Check the API documentation
2. Review the test files for examples
3. Check error logs for debugging
4. Refer to the deployment guide for production issues

## 🎯 Roadmap

### ✅ Completed Features
- [x] JWT Authentication & User Management
- [x] Wallet Creation & Management
- [x] Transaction Tracking & History
- [x] Password Reset System
- [x] Admin Dashboard & User Management
- [x] Activity Logging & Audit Trails
- [x] Transaction Statistics & Analytics
- [x] System Health Monitoring

### 🚧 In Progress
- [ ] Rate limiting implementation
- [ ] Email integration for password reset

### 📋 Future Features
- [ ] Multi-signature wallet support
- [ ] API versioning
- [ ] Docker containerization
- [ ] Kubernetes deployment manifests
- [ ] Real-time WebSocket notifications
- [ ] Mobile SDK development
- [ ] Advanced analytics dashboard

---

**Built with ❤️ using PHP, featuring clean architecture and enterprise-grade security practices.**
