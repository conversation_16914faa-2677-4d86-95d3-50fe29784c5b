# Environment Configuration
# Copy this to .env and update with your actual values

# Database Configuration
DB_HOST=localhost
DB_NAME=tlssc
DB_USER=root
DB_PASS=

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-change-this-in-production-min-256-bits

# API Configuration
API_ENV=production
API_DEBUG=false

# TRON Network Configuration
TRON_NETWORK=mainnet
MASTER_ADDRESS=
MASTER_PRIVATE_KEY=

# Contract Addresses
# Mainnet USDT: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t
USDT_CONTRACT=TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t

# Security
CORS_ORIGINS=https://yourdomain.com
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=60
RATE_LIMIT_WINDOW=3600
