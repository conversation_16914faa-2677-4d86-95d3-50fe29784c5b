<?php
require_once 'config.php';

// Initialize session
FrontendConfig::initSession();

// Check if user is logged in
$isLoggedIn = FrontendConfig::isAuthenticated();
$isAdmin = FrontendConfig::isAdmin();

// Redirect to admin if admin user
if ($isLoggedIn && $isAdmin && !isset($_GET['user_view'])) {
    header('Location: admin.php');
    exit;
}

// Redirect to dashboard if logged in
if ($isLoggedIn && !isset($_GET['logout'])) {
    header('Location: dashboard.php');
    exit;
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TRON Wallet - Login</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <h1>TRON Wallet</h1>
            <p>Secure cryptocurrency wallet management</p>
        </div>

        <div class="auth-tabs">
            <button class="tab-btn active" onclick="showLogin()">Login</button>
            <button class="tab-btn" onclick="showRegister()">Register</button>
        </div>

        <!-- Login Form -->
        <form id="loginForm" class="auth-form active">
            <div class="form-group">
                <label for="loginEmail">Email</label>
                <input type="email" id="loginEmail" name="email" required>
            </div>
            <div class="form-group">
                <label for="loginPassword">Password</label>
                <input type="password" id="loginPassword" name="password" required>
            </div>
            <button type="submit" class="btn btn-primary">Login</button>
            <div class="auth-links">
                <a href="#" onclick="showForgotPassword()">Forgot Password?</a>
            </div>
        </form>

        <!-- Register Form -->
        <form id="registerForm" class="auth-form">
            <div class="form-group">
                <label for="registerEmail">Email</label>
                <input type="email" id="registerEmail" name="email" required>
            </div>
            <div class="form-group">
                <label for="registerPassword">Password</label>
                <input type="password" id="registerPassword" name="password" required>
            </div>
            <div class="form-group">
                <label for="confirmPassword">Confirm Password</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required>
            </div>
            <button type="submit" class="btn btn-primary">Register</button>
        </form>

        <!-- Forgot Password Form -->
        <form id="forgotPasswordForm" class="auth-form">
            <div class="form-group">
                <label for="forgotEmail">Email</label>
                <input type="email" id="forgotEmail" name="email" required>
            </div>
            <button type="submit" class="btn btn-primary">Send Reset Link</button>
            <div class="auth-links">
                <a href="#" onclick="showLogin()">Back to Login</a>
            </div>
        </form>

        <div id="message" class="message"></div>
    </div>

    <script src="js/auth.js"></script>
</body>
</html>
