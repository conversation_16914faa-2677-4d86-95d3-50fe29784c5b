<?php
// Database initialization script
require 'vendor/autoload.php';

use Simbi\Tls\Config\Config;

Config::load();

try {
    $host = Config::get('DB_HOST');
    $db = Config::get('DB_NAME');
    $user = Config::get('DB_USER');
    $pass = Config::get('DB_PASS');
    
    // Connect to MySQL server
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $user, $pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db`");
    $pdo->exec("USE `$db`");
    
    // Execute the main database schema
    $sqlFile = __DIR__ . '/database.sql';
    if (file_exists($sqlFile)) {
        $sql = file_get_contents($sqlFile);
        $pdo->exec($sql);
    }
    
} catch (Exception $e) {
    error_log("Database initialization error: " . $e->getMessage());
    exit(1);
}
