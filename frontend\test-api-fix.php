<?php
require_once 'config.php';
require_once 'api.php';

// Initialize session
FrontendConfig::initSession();

echo "Testing API Fix - Dashboard Data Loading...\n\n";

// Login with test credentials
$api = new APIWrapper();
$result = $api->login('<EMAIL>', 'testflow123');

if (isset($result['success']) && $result['success']) {
    echo "✅ Login successful!\n";
    
    // Store session data
    $_SESSION['user_id'] = $result['user']['id'];
    $_SESSION['email'] = $result['user']['email'];  
    $_SESSION['token'] = $result['token'];
    $_SESSION['is_admin'] = $result['user']['is_admin'] ?? false;
    
    echo "✅ Session stored successfully\n\n";
    
    // Test the individual endpoints that dashboard uses
    echo "Testing dashboard endpoints:\n";
    
    // Test balance endpoint
    echo "1. Testing get_balance... ";
    $balanceResult = $api->getBalance();
    if (isset($balanceResult['success'])) {
        echo "✅ Success\n";
        echo "   Balance: " . ($balanceResult['balance_formatted'] ?? 'N/A') . "\n";
    } else {
        echo "❌ Failed: " . ($balanceResult['error'] ?? 'Unknown error') . "\n";
    }
    
    // Test transaction statistics
    echo "2. Testing get_transaction_statistics... ";
    $statsResult = $api->getTransactionStatistics();
    if (isset($statsResult['success'])) {
        echo "✅ Success\n";
        if (isset($statsResult['statistics'])) {
            $stats = $statsResult['statistics'];
            echo "   Total Transactions: " . ($stats['total_transactions'] ?? '0') . "\n";
            echo "   Total Deposits: " . ($stats['total_deposits'] ?? '0') . "\n";
            echo "   Total Volume: " . ($stats['total_volume'] ?? '0') . "\n";
        }
    } else {
        echo "❌ Failed: " . ($statsResult['error'] ?? 'Unknown error') . "\n";
    }
    
    // Test transactions list
    echo "3. Testing get_transactions... ";
    $transactionsResult = $api->getTransactions(5, 0);
    if (isset($transactionsResult['success'])) {
        echo "✅ Success\n";
        $transactions = $transactionsResult['transactions'] ?? [];
        echo "   Retrieved " . count($transactions) . " transactions\n";
    } else {
        echo "❌ Failed: " . ($transactionsResult['error'] ?? 'Unknown error') . "\n";
    }
    
    echo "\n=== Now testing AJAX endpoints ===\n";
    
    // Simulate AJAX calls
    $_POST = [];
    $ajaxInput = [
        'action' => 'get_balance'
    ];
    
    // Test balance via AJAX
    echo "4. Testing AJAX get_balance... ";
    
    // Capture output
    ob_start();
    
    // Simulate the AJAX request
    $input = $ajaxInput;
    $action = 'get_balance';
    
    if (!isset($_SESSION['token'])) {
        echo json_encode(['error' => 'Not authenticated']);
    } else {
        $result = $api->getBalance();
        echo json_encode($result);
    }
    
    $ajaxOutput = ob_get_clean();
    $ajaxResult = json_decode($ajaxOutput, true);
    
    if ($ajaxResult && isset($ajaxResult['success'])) {
        echo "✅ Success\n";
        echo "   AJAX Response: " . substr($ajaxOutput, 0, 100) . "...\n";
    } else {
        echo "❌ Failed\n";
        echo "   AJAX Output: " . $ajaxOutput . "\n";
    }
    
} else {
    echo "❌ Login failed: " . ($result['error'] ?? 'Unknown error') . "\n";
    echo "Available users in database:\n";
    
    // Try to connect to database to see what users exist
    try {
        $pdo = new PDO('sqlite:../backend/database.sqlite');
        $stmt = $pdo->query('SELECT email FROM users LIMIT 5');
        while ($row = $stmt->fetch()) {
            echo "  - " . $row['email'] . "\n";
        }
    } catch (Exception $e) {
        echo "  Could not check database: " . $e->getMessage() . "\n";
    }
}
?>
