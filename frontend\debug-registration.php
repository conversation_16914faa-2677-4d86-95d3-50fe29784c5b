<?php
// Debug registration endpoint directly
echo "Testing backend registration endpoint directly...\n\n";

$testEmail = 'debug_' . time() . '@example.com';
$testPassword = 'testpass123';

$url = 'http://localhost:8000/api/register';
$data = [
    'email' => $testEmail,
    'password' => $testPassword
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "URL: $url\n";
echo "Data sent: " . json_encode($data) . "\n";
echo "HTTP Code: $httpCode\n";
echo "cURL Error: " . ($error ?: 'None') . "\n";
echo "Raw Response: '$response'\n";
echo "Response Length: " . strlen($response) . "\n\n";

// Try to decode JSON
$decoded = json_decode($response, true);
$jsonError = json_last_error();

echo "JSON Decode Error: " . json_last_error_msg() . "\n";
if ($jsonError === JSON_ERROR_NONE) {
    echo "Decoded Response:\n";
    print_r($decoded);
} else {
    echo "Failed to decode JSON. Raw response:\n";
    echo "'" . $response . "'\n";
    
    // Show first 500 characters if response is too long
    if (strlen($response) > 500) {
        echo "\nFirst 500 characters:\n";
        echo substr($response, 0, 500) . "...\n";
    }
}
?>
