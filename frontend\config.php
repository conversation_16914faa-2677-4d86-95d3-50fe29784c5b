<?php
/**
 * Frontend Configuration
 * 
 * Configuration settings for the TLS Crypto Wallet Frontend
 */

class FrontendConfig {    // API Configuration
    const API_BASE_URL = 'http://localhost:8000'; // Update this to your backend API URL
    const API_TIMEOUT = 30; // API request timeout in seconds
    
    // Session Configuration
    const SESSION_LIFETIME = 3600; // Session timeout in seconds (1 hour)
    const SESSION_NAME = 'tls_session';
    
    // Security Configuration
    const ENABLE_HTTPS_ONLY = false; // Set to true in production
    const SECURE_COOKIES = false; // Set to true in production with HTTPS
    
    // Application Configuration
    const APP_NAME = 'TLS Crypto Wallet';
    const APP_VERSION = '1.0.0';
    const DEFAULT_CURRENCY = 'TRX';
    
    // Pagination Settings
    const DEFAULT_PAGE_SIZE = 20;
    const MAX_PAGE_SIZE = 100;
    
    // File Upload Settings
    const MAX_UPLOAD_SIZE = 5242880; // 5MB in bytes
    const ALLOWED_UPLOAD_TYPES = ['jpg', 'jpeg', 'png', 'gif'];
    
    // Debug Settings
    const DEBUG_MODE = true; // Set to false in production
    const LOG_ERRORS = true;
    
    /**
     * Get API base URL
     */
    public static function getApiUrl() {
        return self::API_BASE_URL;
    }
    
    /**
     * Get session configuration
     */
    public static function getSessionConfig() {
        return [
            'lifetime' => self::SESSION_LIFETIME,
            'name' => self::SESSION_NAME,
            'secure' => self::SECURE_COOKIES,
            'httponly' => true,
            'samesite' => 'Lax'
        ];
    }
    
    /**
     * Initialize session with security settings
     */    public static function initSession() {
        if (session_status() === PHP_SESSION_NONE) {
            $config = self::getSessionConfig();
            
            ini_set('session.cookie_lifetime', $config['lifetime']);
            ini_set('session.cookie_secure', $config['secure']);
            ini_set('session.cookie_httponly', $config['httponly']);
            ini_set('session.cookie_samesite', $config['samesite']);
            ini_set('session.use_strict_mode', 1);
            
            session_name($config['name']);
            session_start();
        }
        
        // Regenerate session ID for security
        if (!isset($_SESSION['regenerated'])) {
            session_regenerate_id(true);
            $_SESSION['regenerated'] = true;
        }
    }
    
    /**
     * Check if user is authenticated
     */
    public static function isAuthenticated() {
        return isset($_SESSION['user_id']) && isset($_SESSION['token']);
    }
    
    /**
     * Check if user is admin
     */
    public static function isAdmin() {
        return self::isAuthenticated() && ($_SESSION['is_admin'] ?? false);
    }
    
    /**
     * Get current user data
     */
    public static function getCurrentUser() {
        if (!self::isAuthenticated()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'email' => $_SESSION['email'],
            'is_admin' => $_SESSION['is_admin'] ?? false,
            'token' => $_SESSION['token']
        ];
    }
    
    /**
     * Set error reporting based on debug mode
     */
    public static function setErrorReporting() {
        if (self::DEBUG_MODE) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
            ini_set('log_errors', 1);
        } else {
            error_reporting(0);
            ini_set('display_errors', 0);
            ini_set('log_errors', self::LOG_ERRORS ? 1 : 0);
        }
    }
    
    /**
     * Get environment-specific configuration
     */
    public static function getEnvironmentConfig() {
        $env = $_ENV['ENVIRONMENT'] ?? 'development';
        
        switch ($env) {
            case 'production':
                return [
                    'api_url' => 'https://api.tlswallet.com',
                    'debug' => false,
                    'secure_cookies' => true,
                    'https_only' => true
                ];
            
            case 'staging':
                return [
                    'api_url' => 'https://staging-api.tlswallet.com',
                    'debug' => true,
                    'secure_cookies' => true,
                    'https_only' => true
                ];
            
            default: // development
                return [
                    'api_url' => 'http://localhost:3000',
                    'debug' => true,
                    'secure_cookies' => false,
                    'https_only' => false
                ];
        }
    }
}

// Initialize configuration
FrontendConfig::setErrorReporting();
FrontendConfig::initSession();
?>
