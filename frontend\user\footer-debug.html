<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Footer Debug Test</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: #f8f9fa;
            padding-bottom: 80px;
        }
        
        .debug-content {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .debug-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .footer-debug {
            position: fixed;
            bottom: 80px;
            right: 20px;
            background: #007bff;
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1001;
        }
        
        /* Force footer to be visible for debugging */
        .footer-menu {
            display: block !important;
            transform: translateY(0) !important;
            opacity: 1 !important;
            visibility: visible !important;
            background: red !important; /* Make it very obvious */
        }
    </style>
</head>
<body>
    <div class="debug-content">
        <div class="debug-info">
            <h2>Footer Debug Test</h2>
            <p>This page tests the footer menu visibility on desktop.</p>
            <p>The footer should appear at the bottom of the screen.</p>
            
            <div id="footerStatus">
                <h3>Footer Status:</h3>
                <ul id="statusList"></ul>
            </div>
        </div>
        
        <div class="debug-info">
            <h3>CSS Debug Information</h3>
            <p>Screen width: <span id="screenWidth"></span>px</p>
            <p>Footer element found: <span id="footerFound"></span></p>
            <p>Footer display property: <span id="footerDisplay"></span></p>
            <p>Footer transform property: <span id="footerTransform"></span></p>
            <p>Footer z-index: <span id="footerZIndex"></span></p>
            <p>Footer position: <span id="footerPosition"></span></p>
        </div>
    </div>

    <div class="footer-debug">
        Footer Debug Info
    </div>

    <!-- Footer Menu -->
    <footer class="footer-menu">
        <div class="footer-nav">
            <a href="dashboard.php" class="footer-btn active">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" stroke="currentColor" stroke-width="2" fill="currentColor"/>
                </svg>
                <span>Dashboard</span>
            </a>
            <a href="wallet.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 18v1a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2" fill="none"/>
                    <path d="M16 8h4a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-4" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="16" cy="12" r="1" fill="currentColor"/>
                </svg>
                <span>Wallet</span>
            </a>
            <a href="transactions.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <rect x="8" y="2" width="8" height="4" rx="1" ry="1" stroke="currentColor" stroke-width="2" fill="none"/>
                    <path d="M9 12h6" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 16h6" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>Transactions</span>
            </a>
            <a href="profile.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" fill="none"/>
                </svg>
                <span>Profile</span>
            </a>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get footer element
            const footer = document.querySelector('.footer-menu');
            const statusList = document.getElementById('statusList');
            
            // Update screen width
            document.getElementById('screenWidth').textContent = window.innerWidth;
            
            if (footer) {
                document.getElementById('footerFound').textContent = 'Yes';
                
                // Get computed styles
                const styles = window.getComputedStyle(footer);
                document.getElementById('footerDisplay').textContent = styles.display;
                document.getElementById('footerTransform').textContent = styles.transform;
                document.getElementById('footerZIndex').textContent = styles.zIndex;
                document.getElementById('footerPosition').textContent = styles.position;
                
                // Add status checks
                const checks = [
                    { name: 'Footer element exists', status: !!footer },
                    { name: 'Display is block', status: styles.display === 'block' },
                    { name: 'Position is fixed', status: styles.position === 'fixed' },
                    { name: 'Z-index is 1000+', status: parseInt(styles.zIndex) >= 1000 },
                    { name: 'Transform is none or translateY(0)', status: styles.transform === 'none' || styles.transform.includes('translateY(0') },
                    { name: 'Bottom is 0', status: styles.bottom === '0px' }
                ];
                
                checks.forEach(check => {
                    const li = document.createElement('li');
                    li.innerHTML = `<span style="color: ${check.status ? 'green' : 'red'}">${check.status ? '✓' : '✗'}</span> ${check.name}`;
                    statusList.appendChild(li);
                });
                
                // Force show the footer
                footer.classList.add('show');
                footer.style.display = 'block';
                footer.style.transform = 'translateY(0)';
                
            } else {
                document.getElementById('footerFound').textContent = 'No';
                document.getElementById('footerDisplay').textContent = 'N/A';
                document.getElementById('footerTransform').textContent = 'N/A';
                document.getElementById('footerZIndex').textContent = 'N/A';
                document.getElementById('footerPosition').textContent = 'N/A';
                
                const li = document.createElement('li');
                li.innerHTML = '<span style="color: red">✗</span> Footer element not found!';
                statusList.appendChild(li);
            }
            
            // Window resize handler
            window.addEventListener('resize', function() {
                document.getElementById('screenWidth').textContent = window.innerWidth;
            });
        });
    </script>
</body>
</html>
