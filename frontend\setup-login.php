<?php
require_once 'config.php';
require_once 'api.php';

// Initialize session
FrontendConfig::initSession();

echo "Setting up login session for web testing...\n";

$api = new APIWrapper();
$result = $api->login('<EMAIL>', 'testflow123');

if (isset($result['success']) && $result['success']) {
    // Store session data
    $_SESSION['user_id'] = $result['user']['id'];
    $_SESSION['email'] = $result['user']['email'];
    $_SESSION['token'] = $result['token'];
    $_SESSION['is_admin'] = $result['user']['is_admin'] ?? false;
    
    echo "✅ Login session established!\n";
    echo "Visit: http://localhost:8889/user/dashboard.php\n";
    echo "Then test logout by clicking the logout button\n";
} else {
    echo "❌ Login failed\n";
}
?>
