<?php
require_once 'config.php';
require_once 'api.php';

echo "=== COMPLETE LOGIN FLOW VERIFICATION ===\n\n";

// Test 1: Start with clean session
echo "1. STARTING WITH CLEAN SESSION\n";
session_start();
session_destroy();
FrontendConfig::initSession();

$isInitiallyAuth = FrontendConfig::isAuthenticated();
echo "Initially authenticated: " . ($isInitiallyAuth ? "❌ YES (should be NO)" : "✅ NO") . "\n\n";

// Test 2: Login
echo "2. TESTING LOGIN\n";
$api = new APIWrapper();
$loginResult = $api->login('<EMAIL>', 'testflow123');

if (isset($loginResult['success']) && $loginResult['success']) {
    echo "✅ API login successful\n";
    
    // Simulate what ajax.php does
    $_SESSION['user_id'] = $loginResult['user']['id'];
    $_SESSION['email'] = $loginResult['user']['email'];
    $_SESSION['token'] = $loginResult['token'];
    $_SESSION['is_admin'] = $loginResult['user']['is_admin'] ?? false;
    
    echo "✅ Session data stored\n";
    
    // Test authentication after login
    $isAuthAfterLogin = FrontendConfig::isAuthenticated();
    echo "Authenticated after login: " . ($isAuthAfterLogin ? "✅ YES" : "❌ NO") . "\n";
    
    $currentUser = FrontendConfig::getCurrentUser();
    echo "Current user email: " . ($currentUser['email'] ?? 'NONE') . "\n\n";
    
    // Test 3: Dashboard access simulation
    echo "3. TESTING DASHBOARD ACCESS\n";
    // Simulate dashboard.php authentication check
    if (FrontendConfig::isAuthenticated()) {
        echo "✅ Dashboard would load (user is authenticated)\n";
        $user = FrontendConfig::getCurrentUser();
        echo "Welcome message would show: Welcome back, " . ($user['email'] ?? 'Unknown') . "!\n\n";
    } else {
        echo "❌ Dashboard would redirect to login\n\n";
    }
    
    // Test 4: Logout simulation
    echo "4. TESTING LOGOUT\n";
    session_destroy();
    echo "✅ Session destroyed\n";
    
    // Re-initialize session (simulating new request after logout)
    FrontendConfig::initSession();
    $isAuthAfterLogout = FrontendConfig::isAuthenticated();
    echo "Authenticated after logout: " . ($isAuthAfterLogout ? "❌ YES (logout failed)" : "✅ NO") . "\n\n";
    
    // Test 5: Verify dashboard redirect after logout
    echo "5. TESTING DASHBOARD ACCESS AFTER LOGOUT\n";
    if (FrontendConfig::isAuthenticated()) {
        echo "❌ Dashboard would still load (logout failed)\n";
    } else {
        echo "✅ Dashboard would redirect to login (logout successful)\n";
    }
    
} else {
    echo "❌ Login failed: " . ($loginResult['error'] ?? 'Unknown error') . "\n";
}

echo "\n=== FLOW VERIFICATION COMPLETE ===\n";
?>
