<?php
// Quick AJAX endpoint test
echo "Testing AJAX endpoint...\n";

$testEmail = 'quicktest_' . time() . '@example.com';

$postData = json_encode([
    'action' => 'register',
    'email' => $testEmail,
    'password' => 'testpass123'
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/json\r\n",
        'content' => $postData
    ]
]);

$response = file_get_contents('http://localhost:8080/ajax.php', false, $context);
echo "Response: " . $response . "\n";

$result = json_decode($response, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "JSON decoded successfully\n";
    print_r($result);
} else {
    echo "JSON decode error: " . json_last_error_msg() . "\n";
}
?>
