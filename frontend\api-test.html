<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test Suite</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        input { padding: 8px; margin: 5px; width: 200px; }
    </style>
</head>
<body>
    <h1>TLS Crypto Wallet - API Test Suite</h1>
    
    <div class="test-section">
        <h2>1. Registration Test</h2>
        <input type="email" id="regEmail" placeholder="Email" value="<EMAIL>">
        <input type="password" id="regPassword" placeholder="Password" value="testpass123">
        <input type="password" id="regConfirmPassword" placeholder="Confirm Password" value="testpass123">
        <button onclick="testRegister()">Test Registration</button>
        <div id="regResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Login Test</h2>
        <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
        <input type="password" id="loginPassword" placeholder="Password" value="testpass123">
        <button onclick="testLogin()">Test Login</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. Backend API Direct Test</h2>
        <button onclick="testBackendHealth()">Test Backend Health</button>
        <button onclick="testDatabaseConnection()">Test Database</button>
        <div id="backendResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. Frontend-Backend Integration</h2>
        <button onclick="testFrontendBackendFlow()">Test Complete Flow</button>
        <div id="integrationResult" class="result"></div>
    </div>

    <script>
        async function testRegister() {
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            const confirmPassword = document.getElementById('regConfirmPassword').value;
            const resultDiv = document.getElementById('regResult');
            
            if (password !== confirmPassword) {
                resultDiv.innerHTML = '<div class="error">Passwords do not match</div>';
                return;
            }
            
            try {
                const response = await fetch('ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'register',
                        email: email,
                        password: password
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `<div class="success">✅ Registration successful!<br>User ID: ${result.user?.id}<br>Email: ${result.user?.email}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Registration failed: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const resultDiv = document.getElementById('loginResult');
            
            try {
                const response = await fetch('ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `<div class="success">✅ Login successful!<br>User: ${result.user?.email}<br>Admin: ${result.user?.is_admin ? 'Yes' : 'No'}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Login failed: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testBackendHealth() {
            const resultDiv = document.getElementById('backendResult');
            
            try {
                // Test if backend is responding
                const response = await fetch('http://localhost:8000/api/register', {
                    method: 'OPTIONS'
                });
                
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="success">✅ Backend server is responding</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Backend server issues</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Backend unreachable: ${error.message}</div>`;
            }
        }

        async function testDatabaseConnection() {
            const resultDiv = document.getElementById('backendResult');
            
            try {
                // Try to register a test user to verify database
                const response = await fetch('http://localhost:8000/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: `test_${Date.now()}@example.com`,
                        password: 'testpass123'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML += '<div class="success">✅ Database connection working</div>';
                } else if (result.error && result.error.includes('already registered')) {
                    resultDiv.innerHTML += '<div class="success">✅ Database connection working (user exists)</div>';
                } else {
                    resultDiv.innerHTML += `<div class="error">❌ Database issue: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">❌ Database error: ${error.message}</div>`;
            }
        }

        async function testFrontendBackendFlow() {
            const resultDiv = document.getElementById('integrationResult');
            resultDiv.innerHTML = '<div class="info">Testing complete integration flow...</div>';
            
            try {
                // Test 1: Registration through frontend
                const regResponse = await fetch('ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'register',
                        email: `integration_${Date.now()}@example.com`,
                        password: 'testpass123'
                    })
                });
                
                const regResult = await regResponse.json();
                
                if (!regResult.success) {
                    resultDiv.innerHTML = `<div class="error">❌ Integration test failed at registration: ${regResult.error}</div>`;
                    return;
                }
                
                // Test 2: Login with created user
                const loginResponse = await fetch('ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: regResult.user.email,
                        password: 'testpass123'
                    })
                });
                
                const loginResult = await loginResponse.json();
                
                if (loginResult.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Complete integration flow working perfectly!<br>✅ Registration → Login → Session management all functional</div>';
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Integration test failed at login: ${loginResult.error}</div>`;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Integration test error: ${error.message}</div>`;
            }
        }

        // Auto-run basic tests on page load
        window.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testBackendHealth();
                setTimeout(testDatabaseConnection, 1000);
            }, 500);
        });
    </script>
</body>
</html>
