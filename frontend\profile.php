<?php
// Redirect to user folder
header('Location: user/profile.php');
exit;
?>
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TRON Wallet - Profile</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1>TRON Wallet</h1>
                <div class="header-actions">
                    <a href="dashboard.php" class="btn btn-outline">Back to Dashboard</a>
                    <a href="index.php?logout=1" class="btn btn-danger">Logout</a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <div class="profile-page">
                <div class="card profile-card">
                    <div class="profile-header">
                        <h2>User Profile</h2>
                        <p>Manage your account settings and preferences</p>
                    </div>

                    <!-- Profile Information -->
                    <div class="profile-section">
                        <h3>Account Information</h3>
                        <div class="profile-info">
                            <div class="profile-field">
                                <label>Email Address:</label>
                                <span id="userEmail"><?php echo htmlspecialchars($user['email'] ?? ''); ?></span>
                            </div>
                            <div class="profile-field">
                                <label>Member Since:</label>
                                <span id="memberSince">Loading...</span>
                            </div>
                            <div class="profile-field">
                                <label>Account Status:</label>
                                <span class="status-badge status-active">Active</span>
                            </div>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div class="profile-section">
                        <h3>Security Settings</h3>
                        <div class="security-actions">
                            <button id="changePasswordBtn" class="btn btn-primary">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 1L15.09 8.26L22 9L17 14.74L18.18 21.02L12 17.77L5.82 21.02L7 14.74L2 9L8.91 8.26L12 1Z" stroke="currentColor" stroke-width="2" fill="none"/>
                                </svg>
                                Change Password
                            </button>
                        </div>
                    </div>

                    <!-- Wallet Information -->
                    <div class="profile-section">
                        <h3>Wallet Information</h3>
                        <div class="wallet-summary">
                            <div class="wallet-stat">
                                <label>Wallet Status:</label>
                                <span id="walletStatus">Loading...</span>
                            </div>
                            <div class="wallet-stat">
                                <label>Current Balance:</label>
                                <span id="profileBalance">Loading...</span>
                            </div>
                            <div class="wallet-stat">
                                <label>Total Transactions:</label>
                                <span id="totalTransactionsProfile">Loading...</span>
                            </div>
                        </div>
                        <div class="wallet-actions">
                            <a href="dashboard.php" class="btn btn-outline">Manage Wallet</a>
                        </div>
                    </div>
                </div>

                <!-- Change Password Form -->
                <div class="card change-password-card" id="changePasswordCard" style="display: none;">
                    <div class="profile-header">
                        <h3>Change Password</h3>
                        <button id="cancelPasswordChange" class="btn btn-outline btn-sm">Cancel</button>
                    </div>
                    
                    <form id="changePasswordForm">
                        <div class="form-group">
                            <label for="currentPassword">Current Password</label>
                            <input type="password" id="currentPassword" name="current_password" required>
                        </div>
                        <div class="form-group">
                            <label for="newPassword">New Password</label>
                            <input type="password" id="newPassword" name="new_password" required>
                            <div class="password-requirements">
                                <small>Password must be at least 8 characters long</small>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="confirmNewPassword">Confirm New Password</label>
                            <input type="password" id="confirmNewPassword" name="confirm_new_password" required>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Update Password</button>
                        </div>
                    </form>
                </div>
            </div>
        </main>

        <div id="message" class="message"></div>
    </div>

    <script src="js/dashboard.js"></script>
    <script>
        // Profile page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            loadUserProfile();
            loadWalletSummary();
            setupPasswordChangeForm();
        });

        function setupPasswordChangeForm() {
            const changePasswordBtn = document.getElementById('changePasswordBtn');
            const changePasswordCard = document.getElementById('changePasswordCard');
            const cancelPasswordChange = document.getElementById('cancelPasswordChange');
            const changePasswordForm = document.getElementById('changePasswordForm');

            if (changePasswordBtn) {
                changePasswordBtn.addEventListener('click', () => {
                    changePasswordCard.style.display = 'block';
                    changePasswordCard.scrollIntoView({ behavior: 'smooth' });
                });
            }

            if (cancelPasswordChange) {
                cancelPasswordChange.addEventListener('click', () => {
                    changePasswordCard.style.display = 'none';
                    changePasswordForm.reset();
                });
            }

            if (changePasswordForm) {
                changePasswordForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    
                    const currentPassword = document.getElementById('currentPassword').value;
                    const newPassword = document.getElementById('newPassword').value;
                    const confirmNewPassword = document.getElementById('confirmNewPassword').value;

                    if (newPassword !== confirmNewPassword) {
                        showMessage('Passwords do not match', 'error');
                        return;
                    }

                    if (newPassword.length < 8) {
                        showMessage('Password must be at least 8 characters long', 'error');
                        return;
                    }

                    try {
                        const response = await apiCall('change_password', {
                            current_password: currentPassword,
                            new_password: newPassword
                        });

                        if (response.success) {
                            showMessage('Password changed successfully!', 'success');
                            changePasswordForm.reset();
                            changePasswordCard.style.display = 'none';
                        } else {
                            showMessage(response.error || 'Failed to change password', 'error');
                        }
                    } catch (error) {
                        console.error('Error changing password:', error);
                        showMessage('Error changing password. Please try again.', 'error');
                    }
                });
            }
        }

        async function loadWalletSummary() {
            try {
                // Load wallet balance
                const balanceResponse = await apiCall('get_balance');
                if (balanceResponse.success) {
                    document.getElementById('walletStatus').textContent = 'Active';
                    document.getElementById('profileBalance').textContent = balanceResponse.balance_formatted + ' TRX';
                } else {
                    document.getElementById('walletStatus').textContent = 'No Wallet';
                    document.getElementById('profileBalance').textContent = 'Create a wallet first';
                }

                // Load transaction count
                const statsResponse = await apiCall('get_transaction_statistics');
                if (statsResponse.success) {
                    document.getElementById('totalTransactionsProfile').textContent = statsResponse.statistics.total_transactions || '0';
                } else {
                    document.getElementById('totalTransactionsProfile').textContent = '0';
                }
            } catch (error) {
                console.error('Error loading wallet summary:', error);
                document.getElementById('walletStatus').textContent = 'Error loading';
                document.getElementById('profileBalance').textContent = 'Error loading';
                document.getElementById('totalTransactionsProfile').textContent = 'Error loading';
            }
        }
    </script>
</body>
</html>
