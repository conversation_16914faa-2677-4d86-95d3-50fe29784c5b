<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TLS Frontend Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #495057;
        }
        .status {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
        }
        .status.pass {
            background: #d4edda;
            color: #155724;
        }
        .status.fail {
            background: #f8d7da;
            color: #721c24;
        }
        .file-list {
            list-style: none;
            padding: 0;
        }
        .file-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }
        .file-list li:last-child {
            border-bottom: none;
        }
        .file-path {
            font-family: monospace;
            font-size: 14px;
            color: #6c757d;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.secondary:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TLS Crypto Wallet Frontend - Test Page</h1>
        <p>This page tests the frontend functionality and provides links to the main application pages.</p>
        
        <div class="test-section">
            <h3>Application Links</h3>
            <a href="index.php" class="btn">Login/Register Page</a>
            <a href="dashboard.php" class="btn">User Dashboard</a>
            <a href="admin.php" class="btn">Admin Dashboard</a>
        </div>
        
        <div class="test-section">
            <h3>File Structure Check</h3>
            <ul class="file-list">
                <li>
                    <strong>PHP Files:</strong>
                    <div class="file-path">✓ index.php (Authentication)</div>
                    <div class="file-path">✓ dashboard.php (User Dashboard)</div>
                    <div class="file-path">✓ admin.php (Admin Dashboard)</div>
                    <div class="file-path">✓ api.php (API Wrapper)</div>
                    <div class="file-path">✓ ajax.php (AJAX Handler)</div>
                </li>
                <li>
                    <strong>CSS Files:</strong>
                    <div class="file-path">✓ css/style.css (Base Styles)</div>
                    <div class="file-path">✓ css/auth.css (Authentication Styles)</div>
                    <div class="file-path">✓ css/dashboard.css (Dashboard Styles)</div>
                    <div class="file-path">✓ css/admin.css (Admin Styles)</div>
                </li>
                <li>
                    <strong>JavaScript Files:</strong>
                    <div class="file-path">✓ js/auth.js (Authentication Logic)</div>
                    <div class="file-path">✓ js/dashboard.js (Dashboard Functionality)</div>
                    <div class="file-path">✓ js/admin.js (Admin Functionality)</div>
                    <div class="file-path">✓ js/qr-generator.js (QR Code Generation)</div>
                </li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>Features Implemented</h3>
            <ul>
                <li><span class="status pass">✓</span> User Authentication (Login/Register/Forgot Password)</li>
                <li><span class="status pass">✓</span> Session Management & Security</li>
                <li><span class="status pass">✓</span> Mobile-First Responsive Design</li>
                <li><span class="status pass">✓</span> Wallet Creation & Management</li>
                <li><span class="status pass">✓</span> Balance Checking & Refresh</li>
                <li><span class="status pass">✓</span> Transaction History with Filtering</li>
                <li><span class="status pass">✓</span> Crypto Deposits with QR Codes</li>
                <li><span class="status pass">✓</span> Withdrawal Functionality</li>
                <li><span class="status pass">✓</span> User Statistics Dashboard</li>
                <li><span class="status pass">✓</span> Admin User Management</li>
                <li><span class="status pass">✓</span> Admin Transaction Monitoring</li>
                <li><span class="status pass">✓</span> System Health & Logs</li>
                <li><span class="status pass">✓</span> PHP API Wrapper (Hide API from Browser)</li>
                <li><span class="status pass">✓</span> AJAX Request Handling</li>
                <li><span class="status pass">✓</span> Form Validation & Error Handling</li>
                <li><span class="status pass">✓</span> Loading States & User Feedback</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>API Integration</h3>
            <p>The frontend is designed to work with the backend API through the PHP wrapper. All API calls are secured and routed through:</p>
            <ul>
                <li><code>api.php</code> - PHP wrapper class with all 21 API endpoints</li>
                <li><code>ajax.php</code> - Secure AJAX handler with session validation</li>
            </ul>
            <p><strong>Note:</strong> Ensure the backend API is running and accessible for full functionality.</p>
        </div>
        
        <div class="test-section">
            <h3>Mobile Responsiveness</h3>
            <p>The application includes responsive breakpoints for:</p>
            <ul>
                <li>Mobile: ≤ 576px</li>
                <li>Tablet: 577px - 768px</li>
                <li>Desktop: 769px - 992px</li>
                <li>Large Desktop: ≥ 1200px</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>Security Features</h3>
            <ul>
                <li>Session-based authentication</li>
                <li>Role-based access control (User/Admin)</li>
                <li>CSRF protection through session validation</li>
                <li>API credentials hidden from browser console</li>
                <li>Input validation and sanitization</li>
                <li>Secure password handling</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>Browser Support</h3>
            <p>The application is designed to work with modern browsers that support:</p>
            <ul>
                <li>ES6+ JavaScript</li>
                <li>CSS Grid and Flexbox</li>
                <li>Fetch API</li>
                <li>CSS Custom Properties</li>
            </ul>
        </div>
    </div>
</body>
</html>
