# TRON Wallet Service API

🚀 **Production-Ready Enterprise TRON Wallet API**

A comprehensive PHP API for TRON blockchain wallet management featuring secure authentication, advanced transaction processing, and administrative controls.

## ✨ Enterprise Features

- **🔐 JWT Authentication**: Secure user registration, login, and password reset
- **💼 Wallet Management**: TRON wallet creation, balance checking, fund sweeping
- **📊 Transaction Engine**: Deposit recording, transaction history, analytics
- **👑 Admin Dashboard**: User management, system statistics, audit logs
- **🛡️ Security First**: Input validation, secure password hashing, rate limiting
- **📈 Production Grade**: Error handling, logging, monitoring, health checks

## 📈 API Statistics

- **22 Production Endpoints** across 4 main modules
- **JWT-based Authentication** with role-based access control
- **PostgreSQL Database** with optimized queries and indexes
- **TRON Blockchain Integration** with mainnet/testnet support
- **RESTful Architecture** following industry best practices

## 🏗️ Architecture

**Enterprise-grade layered architecture:**

```php
src/
├── Config/              # Environment & database configuration
│   ├── Database.php     # PostgreSQL connection management
│   └── Config.php       # Environment variables loader
├── Controllers/         # HTTP request handlers
│   ├── AuthController.php      # Authentication endpoints
│   ├── WalletController.php    # Wallet management
│   ├── TransactionController.php # Transaction processing
│   └── AdminController.php     # Admin panel features
├── Repositories/        # Data access layer (Repository Pattern)
│   ├── UserRepository.php      # User data operations
│   ├── WalletRepository.php    # Wallet data operations
│   ├── TransactionRepository.php # Transaction data operations
│   └── AdminRepository.php     # Admin data operations
├── Services/           # Business logic layer
│   ├── AuthService.php         # JWT & authentication logic
│   ├── TronService.php         # TRON blockchain integration
│   ├── TransactionService.php  # Transaction business logic
│   └── Router.php              # Request routing engine
├── Middleware/         # Security & validation
│   ├── AuthMiddleware.php      # JWT token validation
│   └── AdminMiddleware.php     # Admin role verification
└── index.php          # Application entry point

database.sql           # Production database schema
DEPLOYMENT.md         # Production deployment guide
```

## 🔌 Production API Endpoints

### 🔐 Authentication Module (5 endpoints)
- `POST /api/register` - User registration with email validation
- `POST /api/login` - JWT token authentication
- `POST /api/password-reset-request` - Secure password reset initiation
- `POST /api/password-reset` - Password reset with token validation
- `GET /api/me` - User profile retrieval

### 💼 Wallet Management (4 endpoints)
- `POST /api/create-wallet` - TRON wallet generation
- `POST /api/balance` - Real-time balance checking
- `POST /api/sweep-funds` - Automated fund sweeping
- `POST /api/withdraw` - External fund transfers

### 📊 Transaction Engine (5 endpoints)
- `GET /api/transactions` - Filtered transaction history
- `POST /api/transactions/deposit` - Deposit recording with validation
- `GET /api/transactions/statistics` - Analytics and reporting
- `GET /api/transactions/{id}` - Individual transaction details
- `GET /api/wallets/{id}/transactions` - Wallet-specific transactions

### 👑 Administrative Controls (8 endpoints)
- `GET /api/admin/statistics` - System-wide analytics
- `GET /api/admin/users` - User management dashboard
- `PUT /api/admin/users/{id}/status` - User status management
- `POST /api/admin/users/{id}/promote` - Admin role assignment
- `GET /api/admin/logs` - Audit trail and activity logs
- `GET /api/admin/transactions/statistics` - Transaction analytics
- `GET /api/admin/health` - System health monitoring
- `POST /api/change-password` - Password change functionality

## 🚀 Production Deployment

**⚠️ Important**: This API is production-ready. For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

### Quick Start (Development)

1. **Install Dependencies**
   ```bash
   composer install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your production credentials
   ```

3. **Database Setup**
   ```bash
   # Import the production schema
   mysql -u root -p < database.sql
   ```

4. **Development Server**
   ```bash
   php -S localhost:8000
   ```

### Production Environment Variables

Create `.env` file with production values:

```env
# Database Configuration
DB_HOST=your-production-db-host
DB_NAME=tron_wallet_api
DB_USER=api_user
DB_PASS=secure_password

# TRON Configuration (Production Mainnet)
TRON_NETWORK=mainnet
MASTER_ADDRESS=your_master_wallet_address
MASTER_PRIVATE_KEY=your_master_private_key
USDT_CONTRACT=TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t

# Security Configuration
JWT_SECRET=your-256-bit-secure-secret
API_ENV=production
CORS_ORIGINS=https://yourdomain.com

# Optional: Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=60
RATE_LIMIT_WINDOW=3600
```

## 📊 Production Features

### 🔒 Security Measures
- **JWT Authentication** with configurable expiration
- **Password Hashing** using PHP's password_hash()
- **Input Validation** and sanitization
- **SQL Injection Protection** via prepared statements
- **CORS Configuration** for cross-origin requests
- **Rate Limiting** (configurable)
- **Environment-based Configuration** (no hardcoded secrets)

### 📈 Monitoring & Analytics
- **System Health Checks** via `/api/admin/health`
- **Transaction Statistics** and reporting
- **User Activity Logging** for audit trails
- **Error Logging** with configurable levels
- **Performance Metrics** tracking

### 🛠️ Technical Specifications
- **PHP 8.0+** with modern language features
- **PostgreSQL/MySQL** database support
- **Composer Autoloading** (PSR-4)
- **Repository Pattern** for data access
- **Dependency Injection** for loose coupling
- **Clean URL Routing** with parameterized routes

## 📚 API Documentation

For complete API documentation with request/response examples, see [API_DOCUMENTATION.md](API_DOCUMENTATION.md).

### Example: User Registration
```bash
curl -X POST https://yourdomain.com/api/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "confirm_password": "SecurePass123!"
  }'
```

### Example: Create Wallet (Authenticated)
```bash
curl -X POST https://yourdomain.com/api/create-wallet \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🏢 Enterprise Ready

This API is designed for enterprise deployment with:

- ✅ **Production Security**: No hardcoded credentials, secure defaults
- ✅ **Scalable Architecture**: Repository pattern, modular design
- ✅ **Error Handling**: Comprehensive exception handling and logging
- ✅ **Documentation**: Complete API docs and deployment guides
- ✅ **Testing**: Archived test suite for development validation
- ✅ **Monitoring**: Health checks and administrative controls
