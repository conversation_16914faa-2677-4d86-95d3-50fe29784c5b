<?php
require_once 'config.php';
require_once 'api.php';

// Initialize session
FrontendConfig::initSession();

echo "=== Testing Complete Login Flow ===\n\n";

// Step 1: Test user registration (if needed)
$api = new APIWrapper();
$testEmail = '<EMAIL>';
$testPassword = 'testflow123';

echo "1. Testing Registration...\n";
$registerResult = $api->register($testEmail, $testPassword);

if (isset($registerResult['success']) && $registerResult['success']) {
    echo "✅ Registration successful! User ID: " . $registerResult['user']['id'] . "\n";
} else {
    echo "⚠️  Registration response: " . ($registerResult['error'] ?? 'User may already exist') . "\n";
}

// Step 2: Test login
echo "\n2. Testing Login...\n";
$loginResult = $api->login($testEmail, $testPassword);

if (isset($loginResult['success']) && $loginResult['success']) {
    echo "✅ Login successful!\n";
    echo "User ID: " . $loginResult['user']['id'] . "\n";
    echo "Email: " . $loginResult['user']['email'] . "\n";
    echo "Is Admin: " . ($loginResult['user']['is_admin'] ? 'Yes' : 'No') . "\n";
    echo "Token: " . substr($loginResult['token'], 0, 20) . "...\n";
    
    // Step 3: Store session data (simulating ajax.php login)
    $_SESSION['user_id'] = $loginResult['user']['id'];
    $_SESSION['email'] = $loginResult['user']['email'];
    $_SESSION['token'] = $loginResult['token'];
    $_SESSION['is_admin'] = $loginResult['user']['is_admin'] ?? false;
    
    echo "✅ Session data stored\n";
    
    // Step 4: Test authentication check
    echo "\n3. Testing Authentication Status...\n";
    $isAuth = FrontendConfig::isAuthenticated();
    $isAdmin = FrontendConfig::isAdmin();
    $currentUser = FrontendConfig::getCurrentUser();
    
    echo "Is Authenticated: " . ($isAuth ? "✅ YES" : "❌ NO") . "\n";
    echo "Is Admin: " . ($isAdmin ? "✅ YES" : "❌ NO") . "\n";
    echo "Current User: " . json_encode($currentUser, JSON_PRETTY_PRINT) . "\n";
    
    // Step 5: Test logout
    echo "\n4. Testing Logout...\n";
    session_destroy();
    
    // Re-initialize to test post-logout state
    FrontendConfig::initSession();
    $isAuthAfterLogout = FrontendConfig::isAuthenticated();
    echo "Is Authenticated After Logout: " . ($isAuthAfterLogout ? "❌ STILL LOGGED IN" : "✅ LOGGED OUT") . "\n";
    
    echo "\n✅ Complete login flow test successful!\n";
    echo "\nTest credentials:\n";
    echo "Email: $testEmail\n";
    echo "Password: $testPassword\n";
    
} else {
    echo "❌ Login failed: " . ($loginResult['error'] ?? 'Unknown error') . "\n";
    echo "Response: " . json_encode($loginResult, JSON_PRETTY_PRINT) . "\n";
}
?>
