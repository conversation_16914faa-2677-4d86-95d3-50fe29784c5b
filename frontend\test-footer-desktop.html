<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Footer Desktop Test</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <style>
        /* Test styles to ensure we can see the footer */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        .test-content {
            padding: 20px;
            margin-bottom: 100px;
        }
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 2000;
        }
    </style>
</head>
<body>
    <div class="debug-info">
        <div>Screen Width: <span id="screenWidth"></span>px</div>
        <div>Footer Display: <span id="footerDisplay"></span></div>
        <div>Footer Transform: <span id="footerTransform"></span></div>
        <div>Footer Bottom: <span id="footerBottom"></span></div>
    </div>

    <div class="app-container">
        <div class="test-content">
            <h1>Footer Desktop Visibility Test</h1>
            <p>This page tests whether the footer menu is visible on desktop screens.</p>
            <p>The footer should be visible at the bottom of the screen regardless of screen size.</p>
            
            <div style="height: 800px; background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%); margin: 20px 0; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
                <div style="text-align: center;">
                    <h2>Scroll Test Area</h2>
                    <p>This tall section allows you to scroll and verify the footer remains fixed at the bottom.</p>
                </div>
            </div>
            
            <p>The footer menu below should contain navigation buttons for Dashboard, Wallet, Transactions, Admin, and Profile.</p>
        </div>
    </div>

    <!-- Footer Menu (copied from includes/footer.php) -->
    <footer class="footer-menu">
        <div class="footer-nav">
            <a href="user/dashboard.php" class="footer-btn active">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <polyline points="9,22 9,12 15,12 15,22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Dashboard</span>
            </a>
            <a href="user/wallet.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12V7H5a2 2 0 0 1 0-4h14v4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M3 5v14a2 2 0 0 0 2 2h16v-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M18 12a2 2 0 0 0 0 4h4v-4h-4z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Wallet</span>
            </a>
            <a href="user/transactions.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Transactions</span>
            </a>
            <a href="admin.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Admin</span>
            </a>
            <a href="user/profile.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Profile</span>
            </a>
        </div>
    </footer>

    <script>
        function updateDebugInfo() {
            const footer = document.querySelector('.footer-menu');
            const footerStyles = window.getComputedStyle(footer);
            
            document.getElementById('screenWidth').textContent = window.innerWidth;
            document.getElementById('footerDisplay').textContent = footerStyles.display;
            document.getElementById('footerTransform').textContent = footerStyles.transform;
            document.getElementById('footerBottom').textContent = footerStyles.bottom;
        }
        
        // Update debug info on load and resize
        window.addEventListener('load', updateDebugInfo);
        window.addEventListener('resize', updateDebugInfo);
        
        // Initialize footer menu (copy functionality from dashboard.js)
        function initFooterMenu() {
            const footerMenu = document.querySelector('.footer-menu');
            if (footerMenu) {
                footerMenu.classList.add('show');
                console.log('Footer menu initialized');
            }
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initFooterMenu();
            updateDebugInfo();
        });
    </script>
</body>
</html>
