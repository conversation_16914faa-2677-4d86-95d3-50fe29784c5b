<?php
require_once '../config.php';

// Initialize session
FrontendConfig::initSession();

// Check if user is logged in
if (!FrontendConfig::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

$user = FrontendConfig::getCurrentUser();

// Set variables for header
$pageTitle = 'TRON Wallet - Profile';
$currentPage = 'profile';
$basePath = '.';
$cssPath = 'css';

// Include header
include '../includes/header.php';
?>

            <!-- Profile Content -->
            <div class="profile-page">
                <div class="card profile-card">
                    <div class="profile-header">
                        <h2>User Profile</h2>
                        <p>Manage your account settings and preferences</p>
                    </div>

                    <!-- Profile Information -->
                    <div class="profile-section">
                        <h3>Account Information</h3>
                        <div class="profile-info">
                            <div class="profile-field">
                                <label>Email Address:</label>
                                <span id="userEmail"><?php echo htmlspecialchars($user['email'] ?? ''); ?></span>
                            </div>                            <div class="profile-field">
                                <label>Username:</label>
                                <span id="userName"><?php echo htmlspecialchars($user['email'] ?? ''); ?></span>
                            </div>
                            <div class="profile-field">
                                <label>Account Created:</label>
                                <span id="userCreated"><?php echo isset($user['created_at']) ? date('M j, Y', strtotime($user['created_at'])) : 'N/A'; ?></span>
                            </div>
                            <div class="profile-field">
                                <label>Account Status:</label>
                                <span class="status-active">Active</span>
                            </div>
                        </div>
                    </div>

                    <!-- Password Change -->
                    <div class="profile-section">
                        <h3>Change Password</h3>
                        <form id="changePasswordForm" class="password-form">
                            <div class="form-group">
                                <label for="currentPassword">Current Password</label>
                                <input type="password" id="currentPassword" name="current_password" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="newPassword">New Password</label>
                                <input type="password" id="newPassword" name="new_password" required minlength="8">
                            </div>
                            
                            <div class="form-group">
                                <label for="confirmPassword">Confirm New Password</label>
                                <input type="password" id="confirmPassword" name="confirm_password" required>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">Update Password</button>
                                <button type="reset" class="btn btn-outline">Reset</button>
                            </div>
                        </form>
                    </div>

                    <!-- Security Settings -->
                    <div class="profile-section">
                        <h3>Security Settings</h3>
                        <div class="security-info">
                            <p>Your account security is important. Here are some recommendations:</p>
                            <ul>
                                <li>Use a strong, unique password</li>
                                <li>Never share your login credentials</li>
                                <li>Log out when using shared computers</li>
                                <li>Contact support if you notice any suspicious activity</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Account Actions -->
                    <div class="profile-section">
                        <h3>Account Actions</h3>
                        <div class="account-actions">
                            <a href="dashboard.php" class="btn btn-outline">Back to Dashboard</a>
                            <a href="../index.php?logout=1" class="btn btn-danger">Logout</a>
                        </div>
                    </div>
                </div>
            </div>

<?php 
// Include footer
include '../includes/footer.php';
?>

    <script>
        // Profile page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            const changePasswordForm = document.getElementById('changePasswordForm');
            
            if (changePasswordForm) {
                changePasswordForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const currentPassword = document.getElementById('currentPassword').value;
                    const newPassword = document.getElementById('newPassword').value;
                    const confirmPassword = document.getElementById('confirmPassword').value;
                    
                    // Validate passwords match
                    if (newPassword !== confirmPassword) {
                        showMessage('New passwords do not match', 'error');
                        return;
                    }
                    
                    // Validate password length
                    if (newPassword.length < 8) {
                        showMessage('New password must be at least 8 characters long', 'error');
                        return;
                    }
                    
                    // Make API call to change password
                    apiCall('change_password', {
                        current_password: currentPassword,
                        new_password: newPassword
                    }).then(response => {
                        if (response.success) {
                            showMessage('Password changed successfully', 'success');
                            changePasswordForm.reset();
                        } else {
                            showMessage(response.message || 'Failed to change password', 'error');
                        }
                    }).catch(error => {
                        console.error('Error changing password:', error);
                        showMessage('Error changing password', 'error');
                    });
                });
            }
        });

        // Utility functions
        function showMessage(message, type = 'info') {
            const messageEl = document.getElementById('message');
            if (messageEl) {
                messageEl.textContent = message;
                messageEl.className = `message ${type}`;
                messageEl.style.display = 'block';
                
                setTimeout(() => {
                    messageEl.style.display = 'none';
                }, 5000);
            }
        }

        async function apiCall(endpoint, data = {}) {
            try {
                const response = await fetch('../api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: endpoint,
                        ...data
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('API call failed:', error);
                throw error;
            }
        }
    </script>
</body>
</html>
