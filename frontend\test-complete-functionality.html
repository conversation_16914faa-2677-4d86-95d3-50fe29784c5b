<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-container { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>TLS Frontend - Complete Functionality Test</h1>
    
    <div class="test-container">
        <h2>1. API Connectivity Test</h2>
        <button onclick="testApiConnectivity()">Test API Connection</button>
        <div id="apiTest"></div>
    </div>
    
    <div class="test-container">
        <h2>2. JSON Parsing Test</h2>
        <button onclick="testJsonParsing()">Test JSON Response Parsing</button>
        <div id="jsonTest"></div>
    </div>
    
    <div class="test-container">
        <h2>3. Dashboard API Calls Test</h2>
        <button onclick="testDashboardApis()">Test Dashboard API Endpoints</button>
        <div id="dashboardTest"></div>
    </div>
    
    <div class="test-container">
        <h2>4. Footer Menu Test</h2>
        <button onclick="testFooterMenu()">Test Footer Menu Visibility</button>
        <div id="footerTest"></div>
    </div>
    
    <div class="test-container">
        <h2>5. Page Navigation Test</h2>
        <button onclick="testPageNavigation()">Test Cross-Page Navigation</button>
        <div id="navigationTest"></div>
    </div>

    <script>
        // Test API connectivity
        async function testApiConnectivity() {
            const result = document.getElementById('apiTest');
            result.innerHTML = '<div class="info">Testing API connectivity...</div>';
            
            try {
                const response = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'test_connection' })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    result.innerHTML = `<div class="success">✓ API is reachable<br>Response: ${JSON.stringify(data)}</div>`;
                } else {
                    result.innerHTML = `<div class="error">✗ API error: ${response.status}</div>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="error">✗ API connection failed: ${error.message}</div>`;
            }
        }
        
        // Test JSON parsing
        async function testJsonParsing() {
            const result = document.getElementById('jsonTest');
            result.innerHTML = '<div class="info">Testing JSON response parsing...</div>';
            
            const testCases = [
                { action: 'get_balance', expected: 'error or success response' },
                { action: 'invalid_action', expected: 'error response' },
                { action: 'get_transactions', expected: 'error or success response' }
            ];
            
            let results = [];
            
            for (const testCase of testCases) {
                try {
                    const response = await fetch('/ajax.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testCase)
                    });
                    
                    const data = await response.json();
                    results.push(`✓ ${testCase.action}: Valid JSON - ${JSON.stringify(data).substring(0, 100)}...`);
                } catch (error) {
                    results.push(`✗ ${testCase.action}: JSON parsing failed - ${error.message}`);
                }
            }
            
            const allSuccess = results.every(r => r.startsWith('✓'));
            result.innerHTML = `<div class="${allSuccess ? 'success' : 'error'}">
                <strong>JSON Parsing Test Results:</strong><br>
                ${results.join('<br>')}
            </div>`;
        }
        
        // Test dashboard APIs
        async function testDashboardApis() {
            const result = document.getElementById('dashboardTest');
            result.innerHTML = '<div class="info">Testing dashboard API endpoints...</div>';
            
            const dashboardApis = [
                'get_balance',
                'get_transactions', 
                'get_transaction_statistics',
                'get_profile'
            ];
            
            let results = [];
            
            for (const api of dashboardApis) {
                try {
                    const response = await fetch('/ajax.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ action: api })
                    });
                    
                    const data = await response.json();
                    if (data.error && data.error.includes('Not authenticated')) {
                        results.push(`✓ ${api}: Endpoint responding correctly (auth required)`);
                    } else if (data.success || data.error) {
                        results.push(`✓ ${api}: Valid response received`);
                    } else {
                        results.push(`? ${api}: Unexpected response format`);
                    }
                } catch (error) {
                    results.push(`✗ ${api}: Failed - ${error.message}`);
                }
            }
            
            result.innerHTML = `<div class="info">
                <strong>Dashboard API Test Results:</strong><br>
                ${results.join('<br>')}
            </div>`;
        }
        
        // Test footer menu
        function testFooterMenu() {
            const result = document.getElementById('footerTest');
            result.innerHTML = '<div class="info">Testing footer menu visibility...</div>';
            
            // Check if footer CSS is properly applied
            const checks = [];
            
            // Test 1: Check if dashboard.css can be loaded
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '/css/dashboard.css';
            document.head.appendChild(link);
            
            link.onload = function() {
                checks.push('✓ Dashboard CSS loaded successfully');
                
                // Test 2: Check footer CSS rules
                const testFooter = document.createElement('div');
                testFooter.className = 'footer-menu';
                testFooter.style.cssText = 'position: fixed; bottom: 0; display: block; transform: translateY(0);';
                document.body.appendChild(testFooter);
                
                const styles = window.getComputedStyle(testFooter);
                if (styles.position === 'fixed' && styles.bottom === '0px') {
                    checks.push('✓ Footer positioning CSS working');
                } else {
                    checks.push('✗ Footer positioning CSS not working');
                }
                
                document.body.removeChild(testFooter);
                
                // Test 3: Check body padding
                const bodyStyles = window.getComputedStyle(document.body);
                if (parseInt(bodyStyles.paddingBottom) > 60) {
                    checks.push('✓ Body bottom padding applied');
                } else {
                    checks.push('? Body bottom padding may need adjustment');
                }
                
                const allSuccess = checks.every(c => c.startsWith('✓'));
                result.innerHTML = `<div class="${allSuccess ? 'success' : 'info'}">
                    <strong>Footer Menu Test Results:</strong><br>
                    ${checks.join('<br>')}
                </div>`;
            };
            
            link.onerror = function() {
                result.innerHTML = `<div class="error">✗ Failed to load dashboard CSS</div>`;
            };
        }
        
        // Test page navigation
        function testPageNavigation() {
            const result = document.getElementById('navigationTest');
            result.innerHTML = '<div class="info">Testing page navigation...</div>';
            
            const pages = [
                { url: '/user/dashboard.php', name: 'User Dashboard' },
                { url: '/user/wallet.php', name: 'Wallet Page' },
                { url: '/user/transactions.php', name: 'Transactions Page' },
                { url: '/user/profile.php', name: 'Profile Page' },
                { url: '/admin.php', name: 'Admin Page' }
            ];
            
            let checks = [];
            let completed = 0;
            
            pages.forEach(page => {
                fetch(page.url, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            checks.push(`✓ ${page.name}: Accessible`);
                        } else {
                            checks.push(`✗ ${page.name}: HTTP ${response.status}`);
                        }
                    })
                    .catch(error => {
                        checks.push(`✗ ${page.name}: ${error.message}`);
                    })
                    .finally(() => {
                        completed++;
                        if (completed === pages.length) {
                            const allSuccess = checks.every(c => c.startsWith('✓'));
                            result.innerHTML = `<div class="${allSuccess ? 'success' : 'info'}">
                                <strong>Page Navigation Test Results:</strong><br>
                                ${checks.join('<br>')}
                            </div>`;
                        }
                    });
            });
        }
        
        // Auto-run basic tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                testApiConnectivity();
                setTimeout(() => testJsonParsing(), 1000);
            }, 500);
        });
    </script>
</body>
</html>
