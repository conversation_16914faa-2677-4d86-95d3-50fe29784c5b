<?php
/**
 * Complete Database Schema Initialization
 * Creates all tables including advanced features
 */

require 'vendor/autoload.php';

use Simbi\Tls\Config\Database;
use Simbi\Tls\Config\Config;

// Load configuration
Config::load();

try {
    // Read the complete SQL schema
    $sqlFile = __DIR__ . '/database.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("Database schema file not found: $sqlFile");
    }

    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("Failed to read database schema file");
    }

    // Get PDO connection
    $pdo = Database::getConnection();
    if (!$pdo) {
        throw new Exception("Failed to connect to database");
    }

    // Execute the complete schema
    $pdo->exec($sql);

} catch (Exception $e) {
    error_log("Complete database initialization error: " . $e->getMessage());
    exit(1);
}
