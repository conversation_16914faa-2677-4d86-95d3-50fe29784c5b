// Deposit Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initDepositPage();
});

let walletData = null;

function initDepositPage() {
    setupDepositActions();
    loadDepositData();
    loadRecentDeposits();
}

function setupDepositActions() {
    const copyDepositBtn = document.getElementById('copyDepositBtn');
    
    if (copyDepositBtn) {
        copyDepositBtn.addEventListener('click', copyDepositAddress);
    }
}

async function loadDepositData() {
    try {
        const response = await apiCall('get_wallet');
        if (response.success && response.wallet) {
            walletData = response.wallet;
            updateDepositDisplay();
        } else {
            // No wallet exists
            document.getElementById('depositAddress').textContent = 'Create a wallet first';
            document.getElementById('copyDepositBtn').style.display = 'none';
            document.getElementById('depositQR').style.display = 'none';
        }
    } catch (error) {
        console.error('Error loading wallet data:', error);
        document.getElementById('depositAddress').textContent = 'Error loading wallet';
    }
}

function updateDepositDisplay() {
    if (walletData && walletData.address) {
        document.getElementById('depositAddress').textContent = walletData.address;
        document.getElementById('copyDepositBtn').style.display = 'inline-block';
        
        // Generate QR code
        generateQRCode(walletData.address);
    }
}

function generateQRCode(address) {
    const qrContainer = document.getElementById('depositQR');
    
    if (typeof QRCode !== 'undefined') {
        qrContainer.innerHTML = ''; // Clear existing QR code
        qrContainer.style.display = 'block';
        
        new QRCode(qrContainer, {
            text: address,
            width: 256,
            height: 256,
            colorDark: "#000000",
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.M
        });
    } else {
        console.warn('QRCode library not loaded');
    }
}

function copyDepositAddress() {
    if (walletData && walletData.address) {
        navigator.clipboard.writeText(walletData.address).then(() => {
            showMessage('Deposit address copied to clipboard!', 'success');
        }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = walletData.address;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showMessage('Deposit address copied to clipboard!', 'success');
        });
    }
}

async function loadRecentDeposits() {
    const recentDepositsList = document.getElementById('recentDepositsList');
    
    if (!recentDepositsList) return;
    
    recentDepositsList.innerHTML = '<div class="loading">Loading recent deposits...</div>';
    
    try {
        const response = await apiCall('get_transactions', {
            type: 'deposit',
            limit: 5,
            page: 0
        });
        
        if (response.success) {
            displayRecentDeposits(response.transactions);
        } else {
            recentDepositsList.innerHTML = '<div class="no-data">No deposits found</div>';
        }
    } catch (error) {
        console.error('Error loading recent deposits:', error);
        recentDepositsList.innerHTML = '<div class="no-data">Error loading deposits</div>';
    }
}

function displayRecentDeposits(deposits) {
    const recentDepositsList = document.getElementById('recentDepositsList');
    
    if (!deposits || deposits.length === 0) {
        recentDepositsList.innerHTML = '<div class="no-data">No recent deposits</div>';
        return;
    }
    
    const depositsHTML = deposits.map(deposit => `
        <div class="transaction-item deposit-item">
            <div class="transaction-header">
                <span class="transaction-type deposit">Deposit</span>
                <span class="transaction-status ${deposit.status}">${formatStatus(deposit.status)}</span>
            </div>
            <div class="transaction-details">
                <div class="transaction-amount positive">
                    +${deposit.amount_formatted} TRX
                </div>
                <div class="transaction-info">
                    <div class="transaction-hash">
                        <span class="label">Hash:</span>
                        <span class="hash">${deposit.hash || 'Pending'}</span>
                    </div>
                    <div class="transaction-date">
                        <span class="label">Date:</span>
                        <span class="date">${formatDate(deposit.created_at)}</span>
                    </div>
                    ${deposit.from_address ? `
                    <div class="transaction-address">
                        <span class="label">From:</span>
                        <span class="address">${deposit.from_address}</span>
                    </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `).join('');
    
    recentDepositsList.innerHTML = depositsHTML;
}

function formatStatus(status) {
    const statuses = {
        'pending': 'Pending',
        'confirmed': 'Confirmed',
        'failed': 'Failed'
    };
    return statuses[status] || status;
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

// Shared utility functions
async function apiCall(endpoint, data = null) {
    const response = await fetch('ajax.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: endpoint,
            ...(data || {})
        })
    });
    return await response.json();
}

function showMessage(message, type = 'info') {
    const messageDiv = document.getElementById('message');
    messageDiv.textContent = message;
    messageDiv.className = `message ${type}`;
    messageDiv.style.display = 'block';
    
    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 3000);
}
