// Wallet Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initWalletPage();
});

let walletData = null;

function initWalletPage() {
    setupWalletForms();
    loadWalletData();
}

function setupWalletForms() {
    // Wallet actions
    const createWalletBtn = document.getElementById('createWalletBtn');
    const refreshBalanceBtn = document.getElementById('refreshBalanceBtn');
    const copyAddressBtn = document.getElementById('copyAddressBtn');
    const sweepFundsBtn = document.getElementById('sweepFundsBtn');

    if (createWalletBtn) {
        createWalletBtn.addEventListener('click', createWallet);
    }

    if (refreshBalanceBtn) {
        refreshBalanceBtn.addEventListener('click', loadWalletData);
    }

    if (copyAddressBtn) {
        copyAddressBtn.addEventListener('click', copyToClipboard);
    }

    if (sweepFundsBtn) {
        sweepFundsBtn.addEventListener('click', sweepFunds);
    }

    // Forms
    const withdrawForm = document.getElementById('withdrawForm');

    if (withdrawForm) {
        withdrawForm.addEventListener('submit', handleWithdraw);
    }
}

async function loadWalletData() {
    try {
        const response = await apiCall('get_wallet');
        if (response.success && response.wallet) {
            walletData = response.wallet;
            updateWalletDisplay();
        } else {
            // No wallet exists
            document.getElementById('walletAddress').value = 'No wallet created';
            document.getElementById('walletBalance').value = '0.000000 TRX';
            document.getElementById('createWalletBtn').style.display = 'inline-block';
            document.getElementById('refreshBalanceBtn').style.display = 'none';
            document.getElementById('copyAddressBtn').style.display = 'none';
            document.getElementById('sweepFundsBtn').style.display = 'none';
        }
    } catch (error) {
        console.error('Error loading wallet data:', error);
        showMessage('Error loading wallet data', 'error');
    }
}

function updateWalletDisplay() {
    if (walletData) {
        document.getElementById('walletAddress').value = walletData.address;
        document.getElementById('walletBalance').value = walletData.balance_formatted;
        
        // Show/hide buttons based on wallet existence
        document.getElementById('createWalletBtn').style.display = 'none';
        document.getElementById('refreshBalanceBtn').style.display = 'inline-block';
        document.getElementById('copyAddressBtn').style.display = 'inline-block';
        document.getElementById('sweepFundsBtn').style.display = 'inline-block';
    }
}

async function createWallet() {
    const btn = document.getElementById('createWalletBtn');
    setButtonLoading(btn, true);
    
    try {
        const response = await apiCall('create_wallet');
        if (response.success) {
            showMessage('Wallet created successfully!', 'success');
            await loadWalletData();
        } else {
            showMessage(response.error || 'Failed to create wallet', 'error');
        }
    } catch (error) {
        console.error('Error creating wallet:', error);
        showMessage('Error creating wallet. Please try again.', 'error');
    } finally {
        setButtonLoading(btn, false);
    }
}

async function handleWithdraw(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    const to = formData.get('to');
    const amount = parseFloat(formData.get('amount'));
    
    if (!to || !amount) {
        showMessage('Please fill in all fields', 'error');
        return;
    }
    
    if (amount <= 0) {
        showMessage('Amount must be greater than 0', 'error');
        return;
    }
    
    // Check if wallet has sufficient balance
    if (walletData && amount > parseFloat(walletData.balance)) {
        showMessage('Insufficient balance', 'error');
        return;
    }
    
    setButtonLoading(submitBtn, true);
    
    try {
        const response = await apiCall('withdraw', {
            to: to,
            amount: amount
        });
        
        if (response.success) {
            showMessage('Withdrawal initiated successfully!', 'success');
            form.reset();
            await loadWalletData(); // Refresh balance
        } else {
            showMessage(response.error || 'Failed to process withdrawal', 'error');
        }
    } catch (error) {
        console.error('Error processing withdrawal:', error);
        showMessage('Error processing withdrawal. Please try again.', 'error');
    } finally {
        setButtonLoading(submitBtn, false);
    }
}

async function sweepFunds() {
    if (!confirm('Are you sure you want to sweep all available funds? This action cannot be undone.')) {
        return;
    }
    
    const btn = document.getElementById('sweepFundsBtn');
    setButtonLoading(btn, true);
    
    try {
        const response = await apiCall('sweep_funds');
        if (response.success) {
            showMessage('Funds swept successfully!', 'success');
            await loadWalletData();
        } else {
            showMessage(response.error || 'Failed to sweep funds', 'error');
        }
    } catch (error) {
        console.error('Error sweeping funds:', error);
        showMessage('Error sweeping funds. Please try again.', 'error');
    } finally {
        setButtonLoading(btn, false);
    }
}

function copyToClipboard() {
    if (walletData && walletData.address) {
        navigator.clipboard.writeText(walletData.address).then(() => {
            showMessage('Address copied to clipboard!', 'success');
        }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = walletData.address;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showMessage('Address copied to clipboard!', 'success');
        });
    }
}

// Shared utility functions
async function apiCall(endpoint, data = null) {
    const response = await fetch('ajax.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: endpoint,
            ...(data || {})
        })
    });
    return await response.json();
}

function showMessage(message, type = 'info') {
    const messageDiv = document.getElementById('message');
    messageDiv.textContent = message;
    messageDiv.className = `message ${type}`;
    messageDiv.style.display = 'block';
    
    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 3000);
}

function setButtonLoading(button, loading) {
    if (loading) {
        button.disabled = true;
        button.dataset.originalText = button.textContent;
        button.textContent = 'Loading...';
    } else {
        button.disabled = false;
        button.textContent = button.dataset.originalText || button.textContent;
    }
}
