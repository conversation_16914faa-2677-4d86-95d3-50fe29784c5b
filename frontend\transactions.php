<?php
// Redirect to user folder
header('Location: user/transactions.php');
exit;
?>
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TRON Wallet - Transaction History</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1>TRON Wallet</h1>
                <div class="header-actions">
                    <a href="dashboard.php" class="btn btn-outline">Dashboard</a>
                    <a href="profile.php" class="btn btn-outline">Profile</a>
                    <a href="index.php?logout=1" class="btn btn-danger">Logout</a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <div class="transactions-page">
                <div class="page-header">
                    <h2>Transaction History</h2>
                    <p>View all your wallet transactions</p>
                </div>

                <div class="card">
                    <div class="transactions-header">
                        <h3>All Transactions</h3>
                        <div class="transaction-filters">
                            <select id="transactionType">
                                <option value="">All Types</option>
                                <option value="deposit">Deposits</option>
                                <option value="withdrawal">Withdrawals</option>
                                <option value="sweep">Sweeps</option>
                            </select>
                            <select id="transactionStatus">
                                <option value="">All Status</option>
                                <option value="confirmed">Confirmed</option>
                                <option value="pending">Pending</option>
                                <option value="failed">Failed</option>
                            </select>
                        </div>
                    </div>
                    <div id="transactionsList">Loading...</div>
                    <div class="pagination">
                        <button id="prevPage" class="btn btn-outline">Previous</button>
                        <span id="pageInfo">Page 1</span>
                        <button id="nextPage" class="btn btn-outline">Next</button>
                    </div>
                </div>
            </div>
        </main>

        <div id="message" class="message"></div>
    </div>

    <!-- Footer Menu -->
    <footer class="footer-menu">
        <div class="footer-nav">
            <a href="dashboard.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" stroke="currentColor" stroke-width="2" fill="currentColor"/>
                </svg>
                <span>Dashboard</span>
            </a>
            <a href="wallet.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 18v1a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2" fill="none"/>
                    <path d="M16 8h4a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-4" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="16" cy="12" r="1" fill="currentColor"/>
                </svg>
                <span>Wallet</span>
            </a>
            <a href="transactions.php" class="footer-btn active">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <rect x="8" y="2" width="8" height="4" rx="1" ry="1" stroke="currentColor" stroke-width="2" fill="none"/>
                    <path d="M9 12h6" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 16h6" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>Transactions</span>
            </a>
            <a href="deposit.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2v20" stroke="currentColor" stroke-width="2"/>
                    <path d="M17 7l-5-5-5 5" stroke="currentColor" stroke-width="2"/>
                    <path d="M3 17h18" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>Deposit</span>
            </a>
            <a href="profile.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" fill="none"/>
                </svg>
                <span>Profile</span>
            </a>
        </div>
    </footer>

    <script src="js/transactions.js"></script>
</body>
</html>
