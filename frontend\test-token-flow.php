<?php
require_once 'config.php';
require_once 'api.php';

// Initialize session
FrontendConfig::initSession();

echo "Testing Token Flow...\n\n";

// Login with test credentials
$api = new APIWrapper();

echo "Before login - Session contents:\n";
print_r($_SESSION);

$result = $api->login('<EMAIL>', 'testflow123');

echo "\nLogin result:\n";
print_r($result);

if (isset($result['success']) && $result['success']) {
    echo "\n✅ Login successful!\n";
    
    // Store session data
    $_SESSION['user_id'] = $result['user']['id'];
    $_SESSION['email'] = $result['user']['email'];  
    $_SESSION['token'] = $result['token'];
    $_SESSION['is_admin'] = $result['user']['is_admin'] ?? false;
    
    echo "\nAfter login - Session contents:\n";
    print_r($_SESSION);
    
    // Create a new API wrapper instance to pick up the token
    $api2 = new APIWrapper();
    
    echo "\nTesting balance with new API instance...\n";
    $balanceResult = $api2->getBalance();
    print_r($balanceResult);
    
} else {
    echo "\n❌ Login failed\n";
    print_r($result);
}
?>
