<?php
// Redirect to user folder
header('Location: user/dashboard.php');
exit;
?>
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TRON Wallet - Dashboard</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1>TRON Wallet</h1>                <div class="header-actions">
                    <?php if ($isAdmin): ?>
                        <a href="admin.php" class="btn btn-secondary">Admin Panel</a>
                    <?php endif; ?>
                    <a href="profile.php" class="btn btn-outline">Profile</a>
                    <a href="index.php?logout=1" class="btn btn-danger">Logout</a>
                </div>
            </div>
        </header>        <!-- Navigation -->
        <nav class="app-nav">
            <a href="dashboard.php" class="nav-btn active">Dashboard</a>
            <a href="wallet.php" class="nav-btn">Wallet</a>
            <a href="transactions.php" class="nav-btn">Transactions</a>
            <a href="deposit.php" class="nav-btn">Deposit</a>
        </nav>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Dashboard Tab -->
            <div id="dashboard" class="tab-content active">
                <div class="dashboard-grid">
                    <!-- Balance Card -->
                    <div class="card balance-card">
                        <h3>Total Balance</h3>
                        <div class="balance-amount" id="totalBalance">Loading...</div>
                        <div class="balance-currency">TRX</div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="card stats-card">
                        <h3>Quick Stats</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-label">Total Transactions</span>
                                <span class="stat-value" id="totalTransactions">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Total Deposits</span>
                                <span class="stat-value" id="totalDeposits">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Total Volume</span>
                                <span class="stat-value" id="totalVolume">0 TRX</span>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Transactions -->
                    <div class="card recent-transactions">
                        <h3>Recent Transactions</h3>
                        <div id="recentTransactionsList">Loading...</div>
                    </div>                </div>
            </div>
        </main>

        <div id="message" class="message"></div>
    </div>    <!-- Sticky Footer Menu -->
    <footer class="footer-menu">
        <div class="footer-nav">
            <a href="dashboard.php" class="footer-btn active">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" stroke="currentColor" stroke-width="2" fill="currentColor"/>
                </svg>
                <span>Dashboard</span>
            </a>
            <a href="wallet.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 18v1a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2" fill="none"/>
                    <path d="M16 8h4a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-4" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="16" cy="12" r="1" fill="currentColor"/>
                </svg>
                <span>Wallet</span>
            </a>
            <a href="transactions.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <rect x="8" y="2" width="8" height="4" rx="1" ry="1" stroke="currentColor" stroke-width="2" fill="none"/>
                    <path d="M9 12h6" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 16h6" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>Transactions</span>
            </a>
            <a href="deposit.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2v20" stroke="currentColor" stroke-width="2"/>
                    <path d="M17 7l-5-5-5 5" stroke="currentColor" stroke-width="2"/>
                    <path d="M3 17h18" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>Deposit</span>
            </a><a href="profile.php" class="footer-btn" id="profileFooterBtn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" fill="none"/>
                </svg>
                <span>Profile</span>
            </a>
        </div>    </footer>

    <script src="js/qr-generator.js"></script>
    <script src="js/dashboard-simple.js"></script>
</body>
</html>
