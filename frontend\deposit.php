<?php
// Redirect to user folder
header('Location: user/deposit.php');
exit;
?>
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TRON Wallet - Deposit Funds</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1>TRON Wallet</h1>
                <div class="header-actions">
                    <a href="dashboard.php" class="btn btn-outline">Dashboard</a>
                    <a href="profile.php" class="btn btn-outline">Profile</a>
                    <a href="index.php?logout=1" class="btn btn-danger">Logout</a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <div class="deposit-page">
                <div class="page-header">
                    <h2>Deposit Funds</h2>
                    <p>Send TRX to your wallet address</p>
                </div>

                <div class="card">
                    <h3>Deposit Instructions</h3>
                    <div class="deposit-info">
                        <p>Send TRX to your wallet address to make a deposit:</p>
                        <div class="deposit-address">
                            <div class="address-container">
                                <label for="depositAddress">Your Deposit Address:</label>
                                <div class="address-display-container">
                                    <div id="depositAddress" class="deposit-address-text">Create a wallet first</div>
                                    <button id="copyDepositBtn" class="btn btn-outline btn-sm" style="display: none;">Copy Address</button>
                                </div>
                            </div>
                        </div>
                        <div class="deposit-qr" id="depositQR" style="display: none;">
                            <!-- QR code will be generated here -->
                        </div>
                        
                        <div class="deposit-warnings">
                            <h4>Important Notes:</h4>
                            <ul>
                                <li>Only send TRX to this address</li>
                                <li>Deposits are automatically detected and credited to your account</li>
                                <li>Minimum deposit amount: 1 TRX</li>
                                <li>Network confirmations required: 19 blocks</li>
                                <li>Do not send tokens other than TRX to this address</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="manual-deposit">
                        <h4>Manual Deposit Record</h4>
                        <p>Deposits are automatically recorded when detected. However, if you have made a deposit and it has not been recorded, you can reach out to support.</p>
                        <div class="support-info">
                            <p>For support inquiries, please contact our support team with your transaction hash (TXID) and deposit details.</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Deposits -->
                <div class="card">
                    <h3>Recent Deposits</h3>
                    <div id="recentDepositsList">Loading...</div>
                </div>
            </div>
        </main>

        <div id="message" class="message"></div>
    </div>

    <!-- Footer Menu -->
    <footer class="footer-menu">
        <div class="footer-nav">
            <a href="dashboard.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" stroke="currentColor" stroke-width="2" fill="currentColor"/>
                </svg>
                <span>Dashboard</span>
            </a>
            <a href="wallet.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 18v1a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2" fill="none"/>
                    <path d="M16 8h4a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-4" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="16" cy="12" r="1" fill="currentColor"/>
                </svg>
                <span>Wallet</span>
            </a>
            <a href="transactions.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <rect x="8" y="2" width="8" height="4" rx="1" ry="1" stroke="currentColor" stroke-width="2" fill="none"/>
                    <path d="M9 12h6" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 16h6" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>Transactions</span>
            </a>
            <a href="deposit.php" class="footer-btn active">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2v20" stroke="currentColor" stroke-width="2"/>
                    <path d="M17 7l-5-5-5 5" stroke="currentColor" stroke-width="2"/>
                    <path d="M3 17h18" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>Deposit</span>
            </a>
            <a href="profile.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" fill="none"/>
                </svg>
                <span>Profile</span>
            </a>
        </div>
    </footer>

    <script src="js/qr-generator.js"></script>
    <script src="js/deposit.js"></script>
</body>
</html>
