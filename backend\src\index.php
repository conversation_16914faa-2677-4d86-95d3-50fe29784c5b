<?php
// Tron Wallet Service API
// Clean, organized API using Repository Pattern

require __DIR__ . '/../vendor/autoload.php';

use Simbi\Tls\Config\Database;
use Simbi\Tls\Config\Config;
use Simbi\Tls\Controllers\WalletController;
use Simbi\Tls\Controllers\AuthController;
use Simbi\Tls\Controllers\TransactionController;
use Simbi\Tls\Controllers\AdminController;
use Simbi\Tls\Repositories\WalletRepository;
use Simbi\Tls\Repositories\UserRepository;
use Simbi\Tls\Repositories\TransactionRepository;
use Simbi\Tls\Repositories\AdminRepository;
use Simbi\Tls\Services\TronService;
use Simbi\Tls\Services\Router;
use Simbi\Tls\Services\AuthService;
use Simbi\Tls\Services\TransactionService;
use Simbi\Tls\Middleware\AuthMiddleware;
use Simbi\Tls\Middleware\AdminMiddleware;

// Load configuration
Config::load();

// CORS headers for API access
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Initialize dependencies
    $pdo = Database::getConnection();
    
    if (!$pdo) {
        http_response_code(500);
        echo json_encode(['error' => 'Database connection failed']);
        exit;
    }    // Initialize repositories
    $walletRepository = new WalletRepository($pdo);
    $userRepository = new UserRepository($pdo);
    $transactionRepository = new TransactionRepository($pdo);
    $adminRepository = new AdminRepository($pdo);
      // Initialize services
    $tronService = new TronService();
    $authService = new AuthService($userRepository, $walletRepository, $tronService);
    $transactionService = new TransactionService($transactionRepository, $walletRepository);
    
    // Initialize controllers
    $walletController = new WalletController($walletRepository, $tronService);
    $authController = new AuthController($authService);
    $transactionController = new TransactionController($transactionService);
    $adminController = new AdminController($adminRepository, $transactionService);
    
    // Initialize middleware
    $authMiddleware = new AuthMiddleware($authService);
    $adminMiddleware = new AdminMiddleware($authService);

    // Initialize router
    $router = new Router();    // Authentication routes (no middleware required)
    $router->addRoute('POST', '/api/register', function() use ($authController) {
        $data = Router::getRequestData();
        return $authController->register($data);
    });    $router->addRoute('POST', '/api/login', function() use ($authController) {
        $data = Router::getRequestData();
        return $authController->login($data);
    });

    // Password reset routes (no middleware required)
    $router->addRoute('POST', '/api/password-reset-request', function() use ($authController) {
        $data = Router::getRequestData();
        return $authController->requestPasswordReset($data);
    });

    $router->addRoute('POST', '/api/password-reset', function() use ($authController) {
        $data = Router::getRequestData();
        return $authController->resetPassword($data);
    });

    // Protected routes (require authentication)
    $router->addRoute('GET', '/api/me', function() use ($authController, $authMiddleware) {
        $user = $authMiddleware->authenticate();
        if (isset($user['error'])) {
            return $user;
        }
        return $authController->me($user);
    });

    $router->addRoute('POST', '/api/create-wallet', function() use ($walletController, $authMiddleware) {
        $user = $authMiddleware->authenticate();
        if (isset($user['error'])) {
            return $user;
        }
        return $walletController->createWallet($user);
    });

    $router->addRoute('POST', '/api/sweep-funds', function() use ($walletController, $authMiddleware) {
        $user = $authMiddleware->authenticate();
        if (isset($user['error'])) {
            return $user;
        }
        $data = Router::getRequestData();
        return $walletController->sweepFunds($data, $user);
    });

    $router->addRoute('POST', '/api/withdraw', function() use ($walletController, $authMiddleware) {
        $user = $authMiddleware->authenticate();
        if (isset($user['error'])) {
            return $user;
        }
        $data = Router::getRequestData();
        return $walletController->withdraw($data, $user);
    });

    $router->addRoute('POST', '/api/balance', function() use ($walletController, $authMiddleware) {
        $user = $authMiddleware->authenticate();
        if (isset($user['error'])) {
            return $user;
        }
        return $walletController->getBalance($user);
    });

    // Password change route (requires authentication)
    $router->addRoute('POST', '/api/change-password', function() use ($authController, $authMiddleware) {
        $user = $authMiddleware->authenticate();
        if (isset($user['error'])) {
            return $user;
        }
        $data = Router::getRequestData();
        return $authController->changePassword($data, $user);
    });    // Transaction routes (specific routes first, then parameterized)
    $router->addRoute('GET', '/api/transactions', function() use ($transactionController, $authMiddleware) {
        $user = $authMiddleware->authenticate();
        if (isset($user['error'])) {
            return $user;
        }
        $filters = $_GET;
        return $transactionController->getHistory($user, $filters);
    });

    $router->addRoute('POST', '/api/transactions/deposit', function() use ($transactionController, $authMiddleware) {
        $user = $authMiddleware->authenticate();
        if (isset($user['error'])) {
            return $user;
        }
        $data = Router::getRequestData();
        return $transactionController->recordDeposit($data, $user);
    });

    $router->addRoute('GET', '/api/transactions/statistics', function() use ($transactionController, $authMiddleware) {
        $user = $authMiddleware->authenticate();
        if (isset($user['error'])) {
            return $user;
        }
        return $transactionController->getStatistics($user);
    });

    $router->addRoute('GET', '/api/transactions/{id}', function($params) use ($transactionController, $authMiddleware) {
        $user = $authMiddleware->authenticate();
        if (isset($user['error'])) {
            return $user;
        }
        return $transactionController->getTransactionDetails((int)$params['id'], $user);
    });    
    $router->addRoute('GET', '/api/wallets/{id}/transactions', function($params) use ($transactionController, $authMiddleware) {
        $user = $authMiddleware->authenticate();
        if (isset($user['error'])) {
            return $user;
        }
        return $transactionController->getWalletTransactions((int)$params['id'], $user, $_GET);
    });

    // Admin routes (require admin privileges)
    $router->addRoute('GET', '/api/admin/statistics', function() use ($adminController, $adminMiddleware) {
        $admin = $adminMiddleware->authenticate();
        if (isset($admin['error'])) {
            return $admin;
        }
        return $adminController->getSystemStatistics($admin);
    });

    $router->addRoute('GET', '/api/admin/users', function() use ($adminController, $adminMiddleware) {
        $admin = $adminMiddleware->authenticate();
        if (isset($admin['error'])) {
            return $admin;
        }
        return $adminController->getUserList($admin, $_GET);
    });

    $router->addRoute('PUT', '/api/admin/users/{id}/status', function($params) use ($adminController, $adminMiddleware) {
        $admin = $adminMiddleware->authenticate();
        if (isset($admin['error'])) {
            return $admin;
        }
        $data = Router::getRequestData();
        return $adminController->updateUserStatus((int)$params['id'], $data, $admin);
    });

    $router->addRoute('POST', '/api/admin/users/{id}/promote', function($params) use ($adminController, $adminMiddleware) {
        $admin = $adminMiddleware->authenticate();
        if (isset($admin['error'])) {
            return $admin;
        }
        return $adminController->promoteToAdmin((int)$params['id'], $admin);
    });

    $router->addRoute('GET', '/api/admin/logs', function() use ($adminController, $adminMiddleware) {
        $admin = $adminMiddleware->authenticate();
        if (isset($admin['error'])) {
            return $admin;
        }
        return $adminController->getActivityLogs($admin, $_GET);
    });

    $router->addRoute('GET', '/api/admin/transactions/statistics', function() use ($adminController, $adminMiddleware) {
        $admin = $adminMiddleware->authenticate();
        if (isset($admin['error'])) {
            return $admin;
        }
        return $adminController->getTransactionStatistics($admin);    });

    $router->addRoute('GET', '/api/admin/health', function() use ($adminController, $adminMiddleware) {
        $admin = $adminMiddleware->authenticate();
        if (isset($admin['error'])) {
            return $admin;
        }
        return $adminController->getSystemHealth($admin);
    });

    // Handle the request
    $router->handleRequest();

} catch (Exception $e) {
    error_log("Application error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
