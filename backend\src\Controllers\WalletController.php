<?php

namespace Simbi\Tls\Controllers;

use Simbi\Tls\Repositories\WalletRepository;
use Simbi\Tls\Services\TronService;
use Exception;

class WalletController
{
    private WalletRepository $walletRepository;
    private TronService $tronService;

    public function __construct(WalletRepository $walletRepository, TronService $tronService)
    {
        $this->walletRepository = $walletRepository;
        $this->tronService = $tronService;
    }    
    
    public function createWallet(array $user): array
    {
        $userId = $user['id'];

        try {
            // Check if wallet already exists for this user
            $existingWallet = $this->walletRepository->findByUserId($userId);
            if ($existingWallet) {
                return ['error' => 'Wallet already exists for this user', 'code' => 409];
            }

            // Generate new wallet
            $account = $this->tronService->generateAddress();
            
            // Save to database
            $success = $this->walletRepository->create(
                $userId,
                $account['address'],
                $account['privateKey']
            );

            if (!$success) {
                return ['error' => 'Failed to save wallet to database', 'code' => 500];
            }

            return [
                'success' => true,
                'address' => $account['address'],
                'message' => 'Wallet created successfully'
            ];
        } catch (Exception $e) {
            error_log("Create wallet error: " . $e->getMessage());
            return ['error' => $e->getMessage(), 'code' => 500];
        }
    }    
    
    public function sweepFunds(array $data, array $user): array
    {
        $userId = $user['id'];

        try {
            // Find wallet
            $wallet = $this->walletRepository->findByUserId($userId);

            if (!$wallet) {
                return ['error' => 'Wallet not found', 'code' => 404];
            }

            // Sweep funds
            $result = $this->tronService->sweepFunds(
                $wallet['private_key'],
                $wallet['address']
            );

            return array_merge($result, ['success' => true]);
        } catch (Exception $e) {
            error_log("Sweep funds error: " . $e->getMessage());
            return ['error' => $e->getMessage(), 'code' => 500];
        }
    }    
    
    public function withdraw(array $data, array $user): array
    {
        $to = $data['to'] ?? null;
        $amount = $data['amount'] ?? null;
        $userId = $user['id'];

        if (!$to || !$amount) {
            return ['error' => 'Missing required fields: to, amount', 'code' => 400];
        }

        // Validate amount
        if (!is_numeric($amount) || (float)$amount <= 0) {
            return ['error' => 'Invalid amount', 'code' => 400];
        }

        // Validate address
        if (!$this->tronService->validateAddress($to)) {
            return ['error' => 'Invalid TRON address', 'code' => 400];
        }

        try {
            // Find user's wallet
            $wallet = $this->walletRepository->findByUserId($userId);
            if (!$wallet) {
                return ['error' => 'Wallet not found', 'code' => 404];
            }

            $result = $this->tronService->withdraw($to, $amount, $wallet['private_key']);
            return array_merge($result, ['success' => true]);
        } catch (Exception $e) {
            error_log("Withdraw error: " . $e->getMessage());
            return ['error' => $e->getMessage(), 'code' => 500];
        }
    }    
      public function getBalance(array $user): array
    {
        $userId = $user['id'];

        try {
            $wallet = $this->walletRepository->findByUserId($userId);
            if (!$wallet) {
                return ['error' => 'Wallet not found', 'code' => 404];
            }

            // Get balance directly from database instead of TronWeb
            $balance = $wallet['balance'] ?? '0.000000';
            
            return [
                'success' => true,
                'address' => $wallet['address'],
                'balance' => $balance,
                'balance_formatted' => number_format((float)$balance, 6, '.', '') // Format to 6 decimal places
            ];
        } catch (Exception $e) {
            error_log("Get balance error: " . $e->getMessage());
            return ['error' => $e->getMessage(), 'code' => 500];
        }
    }
}
