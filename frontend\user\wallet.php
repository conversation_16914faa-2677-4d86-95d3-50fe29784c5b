<?php
require_once '../config.php';

// Initialize session
FrontendConfig::initSession();

// Check if user is logged in
if (!FrontendConfig::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

$user = FrontendConfig::getCurrentUser();

// Set variables for header
$pageTitle = 'TRON Wallet - Wallet Management';
$currentPage = 'wallet';
$basePath = '.';
$cssPath = 'css';

// Include header
include '../includes/header.php';
?>

            <!-- Wallet Content -->
            <div class="wallet-page">
                <div class="page-header">
                    <h2>Wallet Management</h2>
                    <p>Manage your TRON wallet and funds</p>
                </div>

                <!-- Wallet Information -->
                <div class="card">
                    <h3>Wallet Information</h3>
                    <div class="wallet-info">
                        <div class="wallet-field">
                            <label for="walletAddress">Wallet Address</label>
                            <input type="text" id="walletAddress" readonly class="address-display" placeholder="No wallet created">
                            <button id="copyAddressBtn" class="btn btn-outline btn-sm" style="display: none;">Copy</button>
                        </div>
                        
                        <div class="wallet-field">
                            <label for="walletBalance">Balance</label>
                            <div class="balance-display">
                                <span id="walletBalance">0.000000</span>
                                <span class="currency">TRX</span>
                                <button id="refreshBalanceBtn" class="btn btn-outline btn-sm">Refresh</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Wallet Actions -->
                <div class="card">
                    <h3>Wallet Actions</h3>
                    <div class="wallet-actions">
                        <button id="createWalletBtn" class="btn btn-primary">Create New Wallet</button>
                        <button id="sweepFundsBtn" class="btn btn-secondary" style="display: none;">Sweep Funds</button>
                    </div>
                </div>

                <!-- Withdrawal Form -->
                <div class="card">
                    <h3>Withdraw Funds</h3>
                    <form id="withdrawForm" class="withdraw-form">
                        <div class="form-group">
                            <label for="withdrawAddress">Recipient Address</label>
                            <input type="text" id="withdrawAddress" name="address" required 
                                   placeholder="Enter TRON address (T...)">
                        </div>
                        
                        <div class="form-group">
                            <label for="withdrawAmount">Amount (TRX)</label>
                            <input type="number" id="withdrawAmount" name="amount" step="0.000001" min="1" required 
                                   placeholder="Enter amount to withdraw">
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Withdraw</button>
                            <button type="reset" class="btn btn-outline">Reset</button>
                        </div>
                    </form>
                </div>
            </div>

<?php 
// Include footer
include '../includes/footer.php';
?>    <script src="js/qr-generator.js"></script>
    <script src="js/wallet.js"></script>
</body>
</html>
