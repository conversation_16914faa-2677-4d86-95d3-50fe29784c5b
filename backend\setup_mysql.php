<?php
// Manual MySQL Database Setup
require 'vendor/autoload.php';

use Simbi\Tls\Config\Config;

Config::load();

echo "=== TLS Crypto Wallet - MySQL Database Setup ===\n\n";

try {
    $host = Config::get('DB_HOST');
    $db = Config::get('DB_NAME');
    $user = Config::get('DB_USER');
    $pass = Config::get('DB_PASS');
    
    echo "Attempting to connect to MySQL server...\n";
    echo "Host: $host\n";
    echo "Database: $db\n";
    echo "User: $user\n\n";
    
    // First, try to connect to MySQL server without specifying database
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $user, $pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "✅ Connected to MySQL server successfully!\n\n";
    
    // Create database if it doesn't exist
    echo "Creating database '$db' if it doesn't exist...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db`");
    $pdo->exec("USE `$db`");
    echo "✅ Database '$db' ready!\n\n";
    
    // Read and execute the SQL schema
    $sqlFile = __DIR__ . '/database.sql';
    if (file_exists($sqlFile)) {
        echo "Executing database schema from database.sql...\n";
        $sql = file_get_contents($sqlFile);
        
        // Remove the CREATE DATABASE and USE statements since we already handled them
        $sql = preg_replace('/CREATE DATABASE IF NOT EXISTS tlssc;/', '', $sql);
        $sql = preg_replace('/USE tlssc;/', '', $sql);
        
        // Split into individual statements and execute
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                try {
                    $pdo->exec($statement);
                    echo "✅ Executed: " . substr($statement, 0, 50) . "...\n";
                } catch (PDOException $e) {
                    // Ignore "table already exists" errors
                    if (strpos($e->getMessage(), 'already exists') === false && 
                        strpos($e->getMessage(), 'Duplicate key name') === false) {
                        echo "⚠️  Warning: " . $e->getMessage() . "\n";
                    }
                }
            }
        }
        
        echo "\n✅ Database schema setup completed!\n";
    } else {
        echo "❌ database.sql file not found!\n";
        exit(1);
    }
    
    // Test the connection by checking tables
    echo "\nVerifying table creation...\n";
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($tables as $table) {
        $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
        echo "✅ Table '$table': $count records\n";
    }
    
    echo "\n🎉 MySQL database setup completed successfully!\n";
    echo "🔗 Connection string: mysql:host=$host;dbname=$db\n";
    
} catch (PDOException $e) {
    echo "❌ Database connection error: " . $e->getMessage() . "\n\n";
    
    if (strpos($e->getMessage(), 'Connection refused') !== false) {
        echo "💡 MySQL server appears to be not running. Please:\n";
        echo "   1. Install MySQL/MariaDB or XAMPP\n";
        echo "   2. Start the MySQL service\n";
        echo "   3. Run this setup script again\n\n";
        
        echo "🔧 Alternative: Use XAMPP for easy MySQL setup:\n";
        echo "   1. Download XAMPP from https://www.apachefriends.org/\n";
        echo "   2. Install and start MySQL service\n";
        echo "   3. Run this script again\n";
    }
    
    exit(1);
} catch (Exception $e) {
    echo "❌ Setup error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
